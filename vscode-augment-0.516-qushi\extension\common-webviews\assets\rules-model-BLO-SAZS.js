var nt=Object.defineProperty;var lt=(r,t,e)=>t in r?nt(r,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[t]=e;var M=(r,t,e)=>lt(r,typeof t!="symbol"?t+"":t,e);import{S as it,i as ct,s as ut,J as O,a as P,a3 as N,a4 as X,d as b,D as j,t as f,q as g,K as W,L as G,M as K,g as dt,ae as pt,a5 as Y,o as H,p as Q,c as B,e as I,E as D,F as V,V as E,G as J,h as A,a0 as U,af as mt,j as ht,W as rt,X as $t,Y as ot,A as Z}from"./SpinnerAugment-VfHtkDdv.js";import{W as gt}from"./IconButtonAugment-BlRCK7lJ.js";import"./BaseTextInput-C9A3t790.js";import{T as ft}from"./TextAreaAugment-BnS2cUNC.js";import{l as yt}from"./lodash-Cs_Exhqr.js";import{R as w,a as Rt}from"./index-C5qylk65.js";const vt=r=>({}),tt=r=>({}),wt=r=>({}),et=r=>({});function st(r){let t,e;return t=new rt({props:{size:1,weight:"light",color:"error",$$slots:{default:[Ft]},$$scope:{ctx:r}}}),{c(){J(t.$$.fragment)},m(o,m){D(t,o,m),e=!0},p(o,m){const h={};4194432&m&&(h.$$scope={dirty:m,ctx:o}),t.$set(h)},i(o){e||(g(t.$$.fragment,o),e=!0)},o(o){f(t.$$.fragment,o),e=!1},d(o){j(t,o)}}}function Ft(r){let t;return{c(){t=ot(r[7])},m(e,o){B(e,t,o)},p(e,o){128&o&&$t(t,e[7])},d(e){e&&b(t)}}}function at(r){let t,e;return t=new rt({props:{size:1,weight:"light",color:"success",$$slots:{default:[_t]},$$scope:{ctx:r}}}),{c(){J(t.$$.fragment)},m(o,m){D(t,o,m),e=!0},i(o){e||(g(t.$$.fragment,o),e=!0)},o(o){f(t.$$.fragment,o),e=!1},d(o){j(t,o)}}}function _t(r){let t;return{c(){t=ot("Saved")},m(e,o){B(e,t,o)},d(e){e&&b(t)}}}function kt(r){let t,e,o,m,h,q,u,k,S,x,$,z,l;const y=r[17].header,d=O(y,r,r[22],et),R=r[17].default,i=O(R,r,r[22],null),F=r[17].title,p=O(F,r,r[22],tt),v=[{variant:r[2]},{size:r[3]},{color:r[4]},{resize:r[5]},{placeholder:"Enter markdown content..."},r[11]];function T(a){r[18](a)}function C(a){r[19](a)}let _={};for(let a=0;a<v.length;a+=1)_=P(_,v[a]);r[0]!==void 0&&(_.textInput=r[0]),r[1]!==void 0&&(_.value=r[1]),u=new ft({props:_}),N.push(()=>X(u,"textInput",T)),N.push(()=>X(u,"value",C)),u.$on("select",r[9]),u.$on("mouseup",r[9]),u.$on("keyup",r[20]),u.$on("input",r[10]),u.$on("keydown",r[21]);let s=!!r[7]&&st(r),c=r[6]&&at(r);return{c(){t=V("div"),e=V("div"),d&&d.c(),o=E(),i&&i.c(),m=E(),h=V("div"),p&&p.c(),q=E(),J(u.$$.fragment),x=E(),$=V("div"),s&&s.c(),z=E(),c&&c.c(),A(e,"class","c-markdown-editor__header svelte-1dcrmc3"),A(h,"class","c-markdown-editor__content svelte-1dcrmc3"),A(t,"class","l-markdown-editor svelte-1dcrmc3"),A($,"class","c-markdown-editor__status svelte-1dcrmc3")},m(a,n){B(a,t,n),I(t,e),d&&d.m(e,null),I(t,o),i&&i.m(t,null),I(t,m),I(t,h),p&&p.m(h,null),I(h,q),D(u,h,null),B(a,x,n),B(a,$,n),s&&s.m($,null),I($,z),c&&c.m($,null),l=!0},p(a,[n]){d&&d.p&&(!l||4194304&n)&&W(d,y,a,a[22],l?K(y,a[22],n,wt):G(a[22]),et),i&&i.p&&(!l||4194304&n)&&W(i,R,a,a[22],l?K(R,a[22],n,null):G(a[22]),null),p&&p.p&&(!l||4194304&n)&&W(p,F,a,a[22],l?K(F,a[22],n,vt):G(a[22]),tt);const L=2108&n?dt(v,[4&n&&{variant:a[2]},8&n&&{size:a[3]},16&n&&{color:a[4]},32&n&&{resize:a[5]},v[4],2048&n&&pt(a[11])]):{};!k&&1&n&&(k=!0,L.textInput=a[0],Y(()=>k=!1)),!S&&2&n&&(S=!0,L.value=a[1],Y(()=>S=!1)),u.$set(L),a[7]?s?(s.p(a,n),128&n&&g(s,1)):(s=st(a),s.c(),g(s,1),s.m($,z)):s&&(H(),f(s,1,1,()=>{s=null}),Q()),a[6]?c?64&n&&g(c,1):(c=at(a),c.c(),g(c,1),c.m($,null)):c&&(H(),f(c,1,1,()=>{c=null}),Q())},i(a){l||(g(d,a),g(i,a),g(p,a),g(u.$$.fragment,a),g(s),g(c),l=!0)},o(a){f(d,a),f(i,a),f(p,a),f(u.$$.fragment,a),f(s),f(c),l=!1},d(a){a&&(b(t),b(x),b($)),d&&d.d(a),i&&i.d(a),p&&p.d(a),j(u),s&&s.d(),c&&c.d()}}}function St(r,t,e){const o=["variant","size","color","resize","textInput","value","selectedText","selectionStart","selectionEnd","saveFunction","debounceValue"];let m,h,q=U(t,o),{$$slots:u={},$$scope:k}=t,{variant:S="surface"}=t,{size:x=2}=t,{color:$}=t,{resize:z="none"}=t,{textInput:l}=t,{value:y=""}=t,{selectedText:d=""}=t,{selectionStart:R=0}=t,{selectionEnd:i=0}=t,{saveFunction:F}=t,{debounceValue:p=2500}=t,v=!1;const T=async()=>{try{F(),e(6,v=!0),clearTimeout(m),m=setTimeout(()=>{e(6,v=!1)},1500)}catch(s){e(7,h=s instanceof Error?s.message:String(s))}};function C(){l&&(e(13,R=l.selectionStart),e(14,i=l.selectionEnd),e(12,d=R!==i?y.substring(R,i):""))}const _=yt.debounce(T,p);return mt(()=>{T()}),r.$$set=s=>{t=P(P({},t),ht(s)),e(11,q=U(t,o)),"variant"in s&&e(2,S=s.variant),"size"in s&&e(3,x=s.size),"color"in s&&e(4,$=s.color),"resize"in s&&e(5,z=s.resize),"textInput"in s&&e(0,l=s.textInput),"value"in s&&e(1,y=s.value),"selectedText"in s&&e(12,d=s.selectedText),"selectionStart"in s&&e(13,R=s.selectionStart),"selectionEnd"in s&&e(14,i=s.selectionEnd),"saveFunction"in s&&e(15,F=s.saveFunction),"debounceValue"in s&&e(16,p=s.debounceValue),"$$scope"in s&&e(22,k=s.$$scope)},[l,y,S,x,$,z,v,h,T,C,_,q,d,R,i,F,p,u,function(s){l=s,e(0,l)},function(s){y=s,e(1,y)},()=>{C()},s=>{(s.key==="Escape"||(s.metaKey||s.ctrlKey)&&s.key==="s")&&(s.preventDefault(),T())},k]}class Bt extends it{constructor(t){super(),ct(this,t,St,kt,ut,{variant:2,size:3,color:4,resize:5,textInput:0,value:1,selectedText:12,selectionStart:13,selectionEnd:14,saveFunction:15,debounceValue:16})}}class Ct{constructor(t){M(this,"_rulesFiles",Z([]));M(this,"_loading",Z(!0));this._msgBroker=t,this.requestRules()}handleMessageFromExtension(t){return!(!t.data||t.data.type!==gt.getRulesListResponse)&&(this._rulesFiles.set(t.data.data),this._loading.set(!1),!0)}async requestRules(){this._loading.set(!0);try{const t=await this._msgBroker.sendToSidecar({type:w.getRulesListRequest,data:{includeGuidelines:!0}});this._rulesFiles.set(t.data.rules)}catch(t){console.error("Failed to get rules list:",t)}finally{this._loading.set(!1)}}async createRule(t){try{const e=await this._msgBroker.sendToSidecar({type:w.createRule,data:{ruleName:t.trim()}});return await this.requestRules(),e.data.createdRule||null}catch(e){throw console.error("Failed to create rule:",e),e}}async getWorkspaceRoot(){try{return(await this._msgBroker.sendToSidecar({type:w.getWorkspaceRoot})).data.workspaceRoot||""}catch(t){return console.error("Failed to get workspace root:",t),""}}async updateRuleContent(t){const e=Rt.formatRuleFileForMarkdown(t);try{await this._msgBroker.sendToSidecar({type:w.updateRuleFile,data:{path:t.path,content:e}})}catch(o){console.error("Failed to update rule file:",o)}await this.requestRules()}async deleteRule(t){try{await this._msgBroker.sendToSidecar({type:w.deleteRule,data:{path:t,confirmed:!0}}),await this.requestRules()}catch(e){throw console.error("Failed to delete rule:",e),e}}async processSelectedPaths(t){try{const e=await this._msgBroker.sendToSidecar({type:w.processSelectedPathsRequest,data:{selectedPaths:t,autoImport:!0}});return await this.requestRules(),{importedRulesCount:e.data.importedRulesCount,directoryOrFile:e.data.directoryOrFile,errors:e.data.errors}}catch(e){throw console.error("Failed to process selected paths:",e),e}}async getAutoImportOptions(){return await this._msgBroker.sendToSidecar({type:w.autoImportRules})}async processAutoImportSelection(t){try{const e=await this._msgBroker.sendToSidecar({type:w.autoImportRulesSelectionRequest,data:{selectedLabel:t.label}});return await this.requestRules(),{importedRulesCount:e.data.importedRulesCount,duplicatesCount:e.data.duplicatesCount,totalAttempted:e.data.totalAttempted,source:e.data.source}}catch(e){throw console.error("Failed to process auto-import selection:",e),e}}getRulesFiles(){return this._rulesFiles}getLoading(){return this._loading}}export{Bt as M,Ct as R};
