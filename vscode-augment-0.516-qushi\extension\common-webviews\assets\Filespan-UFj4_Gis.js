import{S as Y,i as Z,s as K,W as B,D as H,t as u,q as d,E as O,G as P,I as Q,d as _,c as b,N as T,a8 as N,a as U,o as F,p as q,X as L,a9 as D,g as nn,e as w,R as an,aa as en,F as y,V as k,Y as M,h as z,J as R,K as V,L as W,M as X,$ as S}from"./SpinnerAugment-VfHtkDdv.js";import{n as tn,g as cn,a as ln}from"./file-paths-CXmnYUii.js";const on=e=>({}),j=e=>({}),pn=e=>({}),A=e=>({});function E(e){let n;const l=e[11].leftIcon,c=R(l,e,e[12],A);return{c(){c&&c.c()},m(a,t){c&&c.m(a,t),n=!0},p(a,t){c&&c.p&&(!n||4096&t)&&V(c,l,a,a[12],n?X(l,a[12],t,pn):W(a[12]),A)},i(a){n||(d(c,a),n=!0)},o(a){u(c,a),n=!1},d(a){c&&c.d(a)}}}function G(e){let n,l,c;return{c(){n=y("div"),l=y("div"),c=M(e[6]),z(l,"class","c-filespan__dir-text svelte-9pfhnp"),z(n,"class","c-filespan__dir svelte-9pfhnp"),S(n,"growname",e[3])},m(a,t){b(a,n,t),w(n,l),w(l,c)},p(a,t){64&t&&L(c,a[6]),8&t&&S(n,"growname",a[3])},d(a){a&&_(n)}}}function J(e){let n,l;const c=e[11].rightIcon,a=R(c,e,e[12],j);return{c(){n=y("span"),a&&a.c(),z(n,"class","right-icons svelte-9pfhnp")},m(t,s){b(t,n,s),a&&a.m(n,null),l=!0},p(t,s){a&&a.p&&(!l||4096&s)&&V(a,c,t,t[12],l?X(c,t[12],s,on):W(t[12]),j)},i(t){l||(d(a,t),l=!0)},o(t){u(a,t),l=!1},d(t){t&&_(n),a&&a.d(t)}}}function C(e){let n,l,c,a,t,s,h,g,m,x,I,o=e[8].leftIcon&&E(e),r=!e[2]&&G(e),i=e[8].rightIcon&&J(e),v=[{class:h=N(`c-filespan ${e[0]}`)+" svelte-9pfhnp"},{role:g=e[4]?"button":""},{tabindex:"0"}],p={};for(let $=0;$<v.length;$+=1)p=U(p,v[$]);return{c(){n=y(e[5]),o&&o.c(),l=k(),c=y("span"),a=M(e[7]),t=k(),r&&r.c(),s=k(),i&&i.c(),z(c,"class","c-filespan__filename svelte-9pfhnp"),D(e[5])(n,p)},m($,f){b($,n,f),o&&o.m(n,null),w(n,l),w(n,c),w(c,a),w(n,t),r&&r.m(n,null),w(n,s),i&&i.m(n,null),m=!0,x||(I=an(n,"click",function(){en(e[4])&&e[4].apply(this,arguments)}),x=!0)},p($,f){(e=$)[8].leftIcon?o?(o.p(e,f),256&f&&d(o,1)):(o=E(e),o.c(),d(o,1),o.m(n,l)):o&&(F(),u(o,1,1,()=>{o=null}),q()),(!m||128&f)&&L(a,e[7]),e[2]?r&&(r.d(1),r=null):r?r.p(e,f):(r=G(e),r.c(),r.m(n,s)),e[8].rightIcon?i?(i.p(e,f),256&f&&d(i,1)):(i=J(e),i.c(),d(i,1),i.m(n,null)):i&&(F(),u(i,1,1,()=>{i=null}),q()),D(e[5])(n,p=nn(v,[(!m||1&f&&h!==(h=N(`c-filespan ${e[0]}`)+" svelte-9pfhnp"))&&{class:h},(!m||16&f&&g!==(g=e[4]?"button":""))&&{role:g},{tabindex:"0"}]))},i($){m||(d(o),d(i),m=!0)},o($){u(o),u(i),m=!1},d($){$&&_(n),o&&o.d(),r&&r.d(),i&&i.d(),x=!1,I()}}}function rn(e){let n,l,c=e[5],a=e[5]&&C(e);return{c(){a&&a.c(),n=T()},m(t,s){a&&a.m(t,s),b(t,n,s),l=!0},p(t,s){t[5]?c?K(c,t[5])?(a.d(1),a=C(t),c=t[5],a.c(),a.m(n.parentNode,n)):a.p(t,s):(a=C(t),c=t[5],a.c(),a.m(n.parentNode,n)):c&&(a.d(1),a=null,c=t[5])},i(t){l||(d(a,t),l=!0)},o(t){u(a,t),l=!1},d(t){t&&_(n),a&&a.d(t)}}}function sn(e){let n,l;return n=new B({props:{size:e[1],$$slots:{default:[rn]},$$scope:{ctx:e}}}),{c(){P(n.$$.fragment)},m(c,a){O(n,c,a),l=!0},p(c,[a]){const t={};2&a&&(t.size=c[1]),4605&a&&(t.$$scope={dirty:a,ctx:c}),n.$set(t)},i(c){l||(d(n.$$.fragment,c),l=!0)},o(c){u(n.$$.fragment,c),l=!1},d(c){H(n,c)}}}function $n(e,n,l){let c,a,t,s,{$$slots:h={},$$scope:g}=n;const m=Q(h);let{class:x=""}=n,{filepath:I}=n,{size:o=1}=n,{nopath:r=!1}=n,{growname:i=!0}=n,{onClick:v}=n;return e.$$set=p=>{"class"in p&&l(0,x=p.class),"filepath"in p&&l(9,I=p.filepath),"size"in p&&l(1,o=p.size),"nopath"in p&&l(2,r=p.nopath),"growname"in p&&l(3,i=p.growname),"onClick"in p&&l(4,v=p.onClick),"$$scope"in p&&l(12,g=p.$$scope)},e.$$.update=()=>{512&e.$$.dirty&&l(10,c=tn(I)),1024&e.$$.dirty&&l(7,a=cn(c)),1024&e.$$.dirty&&l(6,t=ln(c)),16&e.$$.dirty&&l(5,s=v?"button":"div")},[x,o,r,i,v,s,t,a,m,I,c,h,g]}class mn extends Y{constructor(n){super(),Z(this,n,$n,sn,K,{class:0,filepath:9,size:1,nopath:2,growname:3,onClick:4})}}export{mn as F};
