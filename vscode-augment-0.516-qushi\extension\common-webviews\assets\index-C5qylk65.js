var lo=Object.defineProperty;var uo=(e,t,n)=>t in e?lo(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var c=(e,t,n)=>uo(e,typeof t!="symbol"?t+"":t,n);import{a as co}from"./async-messaging-Cm1y2LK7.js";import{W as v,S as mo,B as ho}from"./IconButtonAugment-BlRCK7lJ.js";import{R as be,P as q,C as R,b as dn,I as Tt,a as L,E as po}from"./message-broker-DxXjuHCW.js";import{C as go}from"./types-CGlLNakm.js";import{n as fo,f as yo,i as _o}from"./file-paths-CXmnYUii.js";import{A as Yn,C as bo,S as ne,i as se,s as re,n as ve,d as A,c as M,e as Yr,f as Dn,h as N,ar as vo,D as X,t as S,q as T,E as J,R as pe,G as Z,al as Pe,z as jr,y as fe,F as Q,a6 as Eo,U as Te,J as ue,K as ce,L as de,M as me,a8 as Ee,ag as Kr,aC as St,a as D,g as He,ae as Ue,a0 as le,I as Wr,j as Oe,W as zr,o as ct,p as dt,V as mt,N as Xr,b as bs,u as To,v as So,w as Io,x as No,H as wo,a3 as jn,$ as xe,O as xo,P as Co,_ as vs,ak as Es,a4 as ko,a5 as Ro}from"./SpinnerAugment-VfHtkDdv.js";import{b as Ao,c as Oo,e as Kn,C as Mo,R as Do,a as $o,f as Fo}from"./CardAugment-CMpdst0l.js";import{B as Uo}from"./BaseTextInput-C9A3t790.js";function Wn(e,t){return!(e===null||typeof e!="object"||!("$typeName"in e)||typeof e.$typeName!="string")&&(t===void 0||t.typeName===e.$typeName)}var g;function Po(){let e=0,t=0;for(let s=0;s<28;s+=7){let r=this.buf[this.pos++];if(e|=(127&r)<<s,!(128&r))return this.assertBounds(),[e,t]}let n=this.buf[this.pos++];if(e|=(15&n)<<28,t=(112&n)>>4,!(128&n))return this.assertBounds(),[e,t];for(let s=3;s<=31;s+=7){let r=this.buf[this.pos++];if(t|=(127&r)<<s,!(128&r))return this.assertBounds(),[e,t]}throw new Error("invalid varint")}function mn(e,t,n){for(let a=0;a<28;a+=7){const o=e>>>a,i=!(!(o>>>7)&&t==0),l=255&(i?128|o:o);if(n.push(l),!i)return}const s=e>>>28&15|(7&t)<<4,r=!!(t>>3);if(n.push(255&(r?128|s:s)),r){for(let a=3;a<31;a+=7){const o=t>>>a,i=!!(o>>>7),l=255&(i?128|o:o);if(n.push(l),!i)return}n.push(t>>>31&1)}}(function(e){e[e.DOUBLE=1]="DOUBLE",e[e.FLOAT=2]="FLOAT",e[e.INT64=3]="INT64",e[e.UINT64=4]="UINT64",e[e.INT32=5]="INT32",e[e.FIXED64=6]="FIXED64",e[e.FIXED32=7]="FIXED32",e[e.BOOL=8]="BOOL",e[e.STRING=9]="STRING",e[e.BYTES=12]="BYTES",e[e.UINT32=13]="UINT32",e[e.SFIXED32=15]="SFIXED32",e[e.SFIXED64=16]="SFIXED64",e[e.SINT32=17]="SINT32",e[e.SINT64=18]="SINT64"})(g||(g={}));const At=4294967296;function Ts(e){const t=e[0]==="-";t&&(e=e.slice(1));const n=1e6;let s=0,r=0;function a(o,i){const l=Number(e.slice(o,i));r*=n,s=s*n+l,s>=At&&(r+=s/At|0,s%=At)}return a(-24,-18),a(-18,-12),a(-12,-6),a(-6),t?Jr(s,r):zn(s,r)}function Ss(e,t){if({lo:e,hi:t}=function(l,u){return{lo:l>>>0,hi:u>>>0}}(e,t),t<=2097151)return String(At*t+e);const n=16777215&(e>>>24|t<<8),s=t>>16&65535;let r=(16777215&e)+6777216*n+6710656*s,a=n+8147497*s,o=2*s;const i=1e7;return r>=i&&(a+=Math.floor(r/i),r%=i),a>=i&&(o+=Math.floor(a/i),a%=i),o.toString()+Is(a)+Is(r)}function zn(e,t){return{lo:0|e,hi:0|t}}function Jr(e,t){return t=~t,e?e=1+~e:t+=1,zn(e,t)}const Is=e=>{const t=String(e);return"0000000".slice(t.length)+t};function Ns(e,t){if(e>=0){for(;e>127;)t.push(127&e|128),e>>>=7;t.push(e)}else{for(let n=0;n<9;n++)t.push(127&e|128),e>>=7;t.push(1)}}function Lo(){let e=this.buf[this.pos++],t=127&e;if(!(128&e))return this.assertBounds(),t;if(e=this.buf[this.pos++],t|=(127&e)<<7,!(128&e))return this.assertBounds(),t;if(e=this.buf[this.pos++],t|=(127&e)<<14,!(128&e))return this.assertBounds(),t;if(e=this.buf[this.pos++],t|=(127&e)<<21,!(128&e))return this.assertBounds(),t;e=this.buf[this.pos++],t|=(15&e)<<28;for(let n=5;128&e&&n<10;n++)e=this.buf[this.pos++];if(128&e)throw new Error("invalid varint");return this.assertBounds(),t>>>0}var ws={};const k=qo();function qo(){const e=new DataView(new ArrayBuffer(8));if(typeof BigInt=="function"&&typeof e.getBigInt64=="function"&&typeof e.getBigUint64=="function"&&typeof e.setBigInt64=="function"&&typeof e.setBigUint64=="function"&&(typeof process!="object"||typeof ws!="object"||ws.BUF_BIGINT_DISABLE!=="1")){const t=BigInt("-9223372036854775808"),n=BigInt("9223372036854775807"),s=BigInt("0"),r=BigInt("18446744073709551615");return{zero:BigInt(0),supported:!0,parse(a){const o=typeof a=="bigint"?a:BigInt(a);if(o>n||o<t)throw new Error(`invalid int64: ${a}`);return o},uParse(a){const o=typeof a=="bigint"?a:BigInt(a);if(o>r||o<s)throw new Error(`invalid uint64: ${a}`);return o},enc(a){return e.setBigInt64(0,this.parse(a),!0),{lo:e.getInt32(0,!0),hi:e.getInt32(4,!0)}},uEnc(a){return e.setBigInt64(0,this.uParse(a),!0),{lo:e.getInt32(0,!0),hi:e.getInt32(4,!0)}},dec:(a,o)=>(e.setInt32(0,a,!0),e.setInt32(4,o,!0),e.getBigInt64(0,!0)),uDec:(a,o)=>(e.setInt32(0,a,!0),e.setInt32(4,o,!0),e.getBigUint64(0,!0))}}return{zero:"0",supported:!1,parse:t=>(typeof t!="string"&&(t=t.toString()),xs(t),t),uParse:t=>(typeof t!="string"&&(t=t.toString()),Cs(t),t),enc:t=>(typeof t!="string"&&(t=t.toString()),xs(t),Ts(t)),uEnc:t=>(typeof t!="string"&&(t=t.toString()),Cs(t),Ts(t)),dec:(t,n)=>function(s,r){let a=zn(s,r);const o=2147483648&a.hi;o&&(a=Jr(a.lo,a.hi));const i=Ss(a.lo,a.hi);return o?"-"+i:i}(t,n),uDec:(t,n)=>Ss(t,n)}}function xs(e){if(!/^-?[0-9]+$/.test(e))throw new Error("invalid int64: "+e)}function Cs(e){if(!/^[0-9]+$/.test(e))throw new Error("invalid uint64: "+e)}function Le(e,t){switch(e){case g.STRING:return"";case g.BOOL:return!1;case g.DOUBLE:case g.FLOAT:return 0;case g.INT64:case g.UINT64:case g.SFIXED64:case g.FIXED64:case g.SINT64:return t?"0":k.zero;case g.BYTES:return new Uint8Array(0);default:return 0}}const Ie=Symbol.for("reflect unsafe local");function Zr(e,t){const n=e[t.localName].case;return n===void 0?n:t.fields.find(s=>s.localName===n)}function Ho(e,t){const n=t.localName;if(t.oneof)return e[t.oneof.localName].case===n;if(t.presence!=2)return e[n]!==void 0&&Object.prototype.hasOwnProperty.call(e,n);switch(t.fieldKind){case"list":return e[n].length>0;case"map":return Object.keys(e[n]).length>0;case"scalar":return!function(s,r){switch(s){case g.BOOL:return r===!1;case g.STRING:return r==="";case g.BYTES:return r instanceof Uint8Array&&!r.byteLength;default:return r==0}}(t.scalar,e[n]);case"enum":return e[n]!==t.enum.values[0].number}throw new Error("message field with implicit presence")}function ot(e,t){return Object.prototype.hasOwnProperty.call(e,t)&&e[t]!==void 0}function Qr(e,t){if(t.oneof){const n=e[t.oneof.localName];return n.case===t.localName?n.value:void 0}return e[t.localName]}function ea(e,t,n){t.oneof?e[t.oneof.localName]={case:t.localName,value:n}:e[t.localName]=n}function Me(e){return e!==null&&typeof e=="object"&&!Array.isArray(e)}function Xn(e,t){var n,s,r,a;if(Me(e)&&Ie in e&&"add"in e&&"field"in e&&typeof e.field=="function"){if(t!==void 0){const o=t,i=e.field();return o.listKind==i.listKind&&o.scalar===i.scalar&&((n=o.message)===null||n===void 0?void 0:n.typeName)===((s=i.message)===null||s===void 0?void 0:s.typeName)&&((r=o.enum)===null||r===void 0?void 0:r.typeName)===((a=i.enum)===null||a===void 0?void 0:a.typeName)}return!0}return!1}function Jn(e,t){var n,s,r,a;if(Me(e)&&Ie in e&&"has"in e&&"field"in e&&typeof e.field=="function"){if(t!==void 0){const o=t,i=e.field();return o.mapKey===i.mapKey&&o.mapKind==i.mapKind&&o.scalar===i.scalar&&((n=o.message)===null||n===void 0?void 0:n.typeName)===((s=i.message)===null||s===void 0?void 0:s.typeName)&&((r=o.enum)===null||r===void 0?void 0:r.typeName)===((a=i.enum)===null||a===void 0?void 0:a.typeName)}return!0}return!1}function Zn(e,t){return Me(e)&&Ie in e&&"desc"in e&&Me(e.desc)&&e.desc.kind==="message"&&(t===void 0||e.desc.typeName==t.typeName)}function yt(e){const t=e.fields[0];return ta(e.typeName)&&t!==void 0&&t.fieldKind=="scalar"&&t.name=="value"&&t.number==1}function ta(e){return e.startsWith("google.protobuf.")&&["DoubleValue","FloatValue","Int64Value","UInt64Value","Int32Value","UInt32Value","BoolValue","StringValue","BytesValue"].includes(e.substring(16))}const Go=999,Vo=998,It=2;function _e(e,t){if(Wn(t,e))return t;const n=function(s){let r;if(function(a){switch(a.file.edition){case Go:return!1;case Vo:return!0;default:return a.fields.some(o=>o.presence!=It&&o.fieldKind!="message"&&!o.oneof)}}(s)){const a=Rs.get(s);let o,i;if(a)({prototype:o,members:i}=a);else{o={},i=new Set;for(const l of s.members)l.kind!="oneof"&&(l.fieldKind!="scalar"&&l.fieldKind!="enum"||l.presence!=It&&(i.add(l),o[l.localName]=hn(l)));Rs.set(s,{prototype:o,members:i})}r=Object.create(o),r.$typeName=s.typeName;for(const l of s.members)if(!i.has(l)){if(l.kind=="field"&&(l.fieldKind=="message"||(l.fieldKind=="scalar"||l.fieldKind=="enum")&&l.presence!=It))continue;r[l.localName]=hn(l)}}else{r={$typeName:s.typeName};for(const a of s.members)a.kind!="oneof"&&a.presence!=It||(r[a.localName]=hn(a))}return r}(e);return t!==void 0&&function(s,r,a){for(const o of s.members){let i,l=a[o.localName];if(l!=null){if(o.kind=="oneof"){const u=Zr(a,o);if(!u)continue;i=u,l=Qr(a,u)}else i=o;switch(i.fieldKind){case"message":l=Qn(i,l);break;case"scalar":l=na(i,l);break;case"list":l=Yo(i,l);break;case"map":l=Bo(i,l)}ea(r,i,l)}}}(e,n,t),n}function na(e,t){return e.scalar==g.BYTES?es(t):t}function Bo(e,t){if(Me(t)){if(e.scalar==g.BYTES)return ks(t,es);if(e.mapKind=="message")return ks(t,n=>Qn(e,n))}return t}function Yo(e,t){if(Array.isArray(t)){if(e.scalar==g.BYTES)return t.map(es);if(e.listKind=="message")return t.map(n=>Qn(e,n))}return t}function Qn(e,t){if(e.fieldKind=="message"&&!e.oneof&&yt(e.message))return na(e.message.fields[0],t);if(Me(t)){if(e.message.typeName=="google.protobuf.Struct"&&e.parent.typeName!=="google.protobuf.Value")return t;if(!Wn(t,e.message))return _e(e.message,t)}return t}function es(e){return Array.isArray(e)?new Uint8Array(e):e}function ks(e,t){const n={};for(const s of Object.entries(e))n[s[0]]=t(s[1]);return n}const jo=Symbol(),Rs=new WeakMap;function hn(e){if(e.kind=="oneof")return{case:void 0};if(e.fieldKind=="list")return[];if(e.fieldKind=="map")return{};if(e.fieldKind=="message")return jo;const t=e.getDefaultValue();return t!==void 0?e.fieldKind=="scalar"&&e.longAsString?t.toString():t:e.fieldKind=="scalar"?Le(e.scalar,e.longAsString):e.enum.values[0].number}const Ko=["FieldValueInvalidError","FieldListRangeError","ForeignFieldError"];class z extends Error{constructor(t,n,s="FieldValueInvalidError"){super(n),this.name=s,this.field=()=>t}}const pn=Symbol.for("@bufbuild/protobuf/text-encoding");function ts(){if(globalThis[pn]==null){const e=new globalThis.TextEncoder,t=new globalThis.TextDecoder;globalThis[pn]={encodeUtf8:n=>e.encode(n),decodeUtf8:n=>t.decode(n),checkUtf8(n){try{return encodeURIComponent(n),!0}catch{return!1}}}}return globalThis[pn]}var O;(function(e){e[e.Varint=0]="Varint",e[e.Bit64=1]="Bit64",e[e.LengthDelimited=2]="LengthDelimited",e[e.StartGroup=3]="StartGroup",e[e.EndGroup=4]="EndGroup",e[e.Bit32=5]="Bit32"})(O||(O={}));const sa=34028234663852886e22,ra=-34028234663852886e22,aa=4294967295,oa=2147483647,ia=-2147483648;class la{constructor(t=ts().encodeUtf8){this.encodeUtf8=t,this.stack=[],this.chunks=[],this.buf=[]}finish(){this.buf.length&&(this.chunks.push(new Uint8Array(this.buf)),this.buf=[]);let t=0;for(let r=0;r<this.chunks.length;r++)t+=this.chunks[r].length;let n=new Uint8Array(t),s=0;for(let r=0;r<this.chunks.length;r++)n.set(this.chunks[r],s),s+=this.chunks[r].length;return this.chunks=[],n}fork(){return this.stack.push({chunks:this.chunks,buf:this.buf}),this.chunks=[],this.buf=[],this}join(){let t=this.finish(),n=this.stack.pop();if(!n)throw new Error("invalid state, fork stack empty");return this.chunks=n.chunks,this.buf=n.buf,this.uint32(t.byteLength),this.raw(t)}tag(t,n){return this.uint32((t<<3|n)>>>0)}raw(t){return this.buf.length&&(this.chunks.push(new Uint8Array(this.buf)),this.buf=[]),this.chunks.push(t),this}uint32(t){for(As(t);t>127;)this.buf.push(127&t|128),t>>>=7;return this.buf.push(t),this}int32(t){return gn(t),Ns(t,this.buf),this}bool(t){return this.buf.push(t?1:0),this}bytes(t){return this.uint32(t.byteLength),this.raw(t)}string(t){let n=this.encodeUtf8(t);return this.uint32(n.byteLength),this.raw(n)}float(t){(function(s){if(typeof s=="string"){const r=s;if(s=Number(s),Number.isNaN(s)&&r!=="NaN")throw new Error("invalid float32: "+r)}else if(typeof s!="number")throw new Error("invalid float32: "+typeof s);if(Number.isFinite(s)&&(s>sa||s<ra))throw new Error("invalid float32: "+s)})(t);let n=new Uint8Array(4);return new DataView(n.buffer).setFloat32(0,t,!0),this.raw(n)}double(t){let n=new Uint8Array(8);return new DataView(n.buffer).setFloat64(0,t,!0),this.raw(n)}fixed32(t){As(t);let n=new Uint8Array(4);return new DataView(n.buffer).setUint32(0,t,!0),this.raw(n)}sfixed32(t){gn(t);let n=new Uint8Array(4);return new DataView(n.buffer).setInt32(0,t,!0),this.raw(n)}sint32(t){return gn(t),Ns(t=(t<<1^t>>31)>>>0,this.buf),this}sfixed64(t){let n=new Uint8Array(8),s=new DataView(n.buffer),r=k.enc(t);return s.setInt32(0,r.lo,!0),s.setInt32(4,r.hi,!0),this.raw(n)}fixed64(t){let n=new Uint8Array(8),s=new DataView(n.buffer),r=k.uEnc(t);return s.setInt32(0,r.lo,!0),s.setInt32(4,r.hi,!0),this.raw(n)}int64(t){let n=k.enc(t);return mn(n.lo,n.hi,this.buf),this}sint64(t){const n=k.enc(t),s=n.hi>>31;return mn(n.lo<<1^s,(n.hi<<1|n.lo>>>31)^s,this.buf),this}uint64(t){const n=k.uEnc(t);return mn(n.lo,n.hi,this.buf),this}}class ns{constructor(t,n=ts().decodeUtf8){this.decodeUtf8=n,this.varint64=Po,this.uint32=Lo,this.buf=t,this.len=t.length,this.pos=0,this.view=new DataView(t.buffer,t.byteOffset,t.byteLength)}tag(){let t=this.uint32(),n=t>>>3,s=7&t;if(n<=0||s<0||s>5)throw new Error("illegal tag: field no "+n+" wire type "+s);return[n,s]}skip(t,n){let s=this.pos;switch(t){case O.Varint:for(;128&this.buf[this.pos++];);break;case O.Bit64:this.pos+=4;case O.Bit32:this.pos+=4;break;case O.LengthDelimited:let r=this.uint32();this.pos+=r;break;case O.StartGroup:for(;;){const[a,o]=this.tag();if(o===O.EndGroup){if(n!==void 0&&a!==n)throw new Error("invalid end group tag");break}this.skip(o,a)}break;default:throw new Error("cant skip wire type "+t)}return this.assertBounds(),this.buf.subarray(s,this.pos)}assertBounds(){if(this.pos>this.len)throw new RangeError("premature EOF")}int32(){return 0|this.uint32()}sint32(){let t=this.uint32();return t>>>1^-(1&t)}int64(){return k.dec(...this.varint64())}uint64(){return k.uDec(...this.varint64())}sint64(){let[t,n]=this.varint64(),s=-(1&t);return t=(t>>>1|(1&n)<<31)^s,n=n>>>1^s,k.dec(t,n)}bool(){let[t,n]=this.varint64();return t!==0||n!==0}fixed32(){return this.view.getUint32((this.pos+=4)-4,!0)}sfixed32(){return this.view.getInt32((this.pos+=4)-4,!0)}fixed64(){return k.uDec(this.sfixed32(),this.sfixed32())}sfixed64(){return k.dec(this.sfixed32(),this.sfixed32())}float(){return this.view.getFloat32((this.pos+=4)-4,!0)}double(){return this.view.getFloat64((this.pos+=8)-8,!0)}bytes(){let t=this.uint32(),n=this.pos;return this.pos+=t,this.assertBounds(),this.buf.subarray(n,n+t)}string(){return this.decodeUtf8(this.bytes())}}function gn(e){if(typeof e=="string")e=Number(e);else if(typeof e!="number")throw new Error("invalid int32: "+typeof e);if(!Number.isInteger(e)||e>oa||e<ia)throw new Error("invalid int32: "+e)}function As(e){if(typeof e=="string")e=Number(e);else if(typeof e!="number")throw new Error("invalid uint32: "+typeof e);if(!Number.isInteger(e)||e>aa||e<0)throw new Error("invalid uint32: "+e)}function De(e,t){const n=e.fieldKind=="list"?Xn(t,e):e.fieldKind=="map"?Jn(t,e):ss(e,t);if(n===!0)return;let s;switch(e.fieldKind){case"list":s=`expected ${da(e)}, got ${F(t)}`;break;case"map":s=`expected ${ma(e)}, got ${F(t)}`;break;default:s=qt(e,t,n)}return new z(e,s)}function Os(e,t,n){const s=ss(e,n);if(s!==!0)return new z(e,`list item #${t+1}: ${qt(e,n,s)}`)}function ss(e,t){return e.scalar!==void 0?ua(t,e.scalar):e.enum!==void 0?e.enum.open?Number.isInteger(t):e.enum.values.some(n=>n.number===t):Zn(t,e.message)}function ua(e,t){switch(t){case g.DOUBLE:return typeof e=="number";case g.FLOAT:return typeof e=="number"&&(!(!Number.isNaN(e)&&Number.isFinite(e))||!(e>sa||e<ra)||`${e.toFixed()} out of range`);case g.INT32:case g.SFIXED32:case g.SINT32:return!(typeof e!="number"||!Number.isInteger(e))&&(!(e>oa||e<ia)||`${e.toFixed()} out of range`);case g.FIXED32:case g.UINT32:return!(typeof e!="number"||!Number.isInteger(e))&&(!(e>aa||e<0)||`${e.toFixed()} out of range`);case g.BOOL:return typeof e=="boolean";case g.STRING:return typeof e=="string"&&(ts().checkUtf8(e)||"invalid UTF8");case g.BYTES:return e instanceof Uint8Array;case g.INT64:case g.SFIXED64:case g.SINT64:if(typeof e=="bigint"||typeof e=="number"||typeof e=="string"&&e.length>0)try{return k.parse(e),!0}catch{return`${e} out of range`}return!1;case g.FIXED64:case g.UINT64:if(typeof e=="bigint"||typeof e=="number"||typeof e=="string"&&e.length>0)try{return k.uParse(e),!0}catch{return`${e} out of range`}return!1}}function qt(e,t,n){return n=typeof n=="string"?`: ${n}`:`, got ${F(t)}`,e.scalar!==void 0?`expected ${function(s){switch(s){case g.STRING:return"string";case g.BOOL:return"boolean";case g.INT64:case g.SINT64:case g.SFIXED64:return"bigint (int64)";case g.UINT64:case g.FIXED64:return"bigint (uint64)";case g.BYTES:return"Uint8Array";case g.DOUBLE:return"number (float64)";case g.FLOAT:return"number (float32)";case g.FIXED32:case g.UINT32:return"number (uint32)";case g.INT32:case g.SFIXED32:case g.SINT32:return"number (int32)"}}(e.scalar)}`+n:e.enum!==void 0?`expected ${e.enum.toString()}`+n:`expected ${ca(e.message)}`+n}function F(e){switch(typeof e){case"object":return e===null?"null":e instanceof Uint8Array?`Uint8Array(${e.length})`:Array.isArray(e)?`Array(${e.length})`:Xn(e)?da(e.field()):Jn(e)?ma(e.field()):Zn(e)?ca(e.desc):Wn(e)?`message ${e.$typeName}`:"object";case"string":return e.length>30?"string":`"${e.split('"').join('\\"')}"`;case"boolean":case"number":return String(e);case"bigint":return String(e)+"n";default:return typeof e}}function ca(e){return`ReflectMessage (${e.typeName})`}function da(e){switch(e.listKind){case"message":return`ReflectList (${e.message.toString()})`;case"enum":return`ReflectList (${e.enum.toString()})`;case"scalar":return`ReflectList (${g[e.scalar]})`}}function ma(e){switch(e.mapKind){case"message":return`ReflectMap (${g[e.mapKey]}, ${e.message.toString()})`;case"enum":return`ReflectMap (${g[e.mapKey]}, ${e.enum.toString()})`;case"scalar":return`ReflectMap (${g[e.mapKey]}, ${g[e.scalar]})`}}function ie(e,t,n=!0){return new ha(e,t,n)}class ha{get sortedFields(){var t;return(t=this._sortedFields)!==null&&t!==void 0?t:this._sortedFields=this.desc.fields.concat().sort((n,s)=>n.number-s.number)}constructor(t,n,s=!0){this.lists=new Map,this.maps=new Map,this.check=s,this.desc=t,this.message=this[Ie]=n??_e(t),this.fields=t.fields,this.oneofs=t.oneofs,this.members=t.members}findNumber(t){return this._fieldsByNumber||(this._fieldsByNumber=new Map(this.desc.fields.map(n=>[n.number,n]))),this._fieldsByNumber.get(t)}oneofCase(t){return tt(this.message,t),Zr(this.message,t)}isSet(t){return tt(this.message,t),Ho(this.message,t)}clear(t){tt(this.message,t),function(n,s){const r=s.localName;if(s.oneof){const a=s.oneof.localName;n[a].case===r&&(n[a]={case:void 0})}else if(s.presence!=2)delete n[r];else switch(s.fieldKind){case"map":n[r]={};break;case"list":n[r]=[];break;case"enum":n[r]=s.enum.values[0].number;break;case"scalar":n[r]=Le(s.scalar,s.longAsString)}}(this.message,t)}get(t){tt(this.message,t);const n=Qr(this.message,t);switch(t.fieldKind){case"list":let s=this.lists.get(t);return s&&s[Ie]===n||this.lists.set(t,s=new Wo(t,n,this.check)),s;case"map":let r=this.maps.get(t);return r&&r[Ie]===n||this.maps.set(t,r=new zo(t,n,this.check)),r;case"message":return as(t,n,this.check);case"scalar":return n===void 0?Le(t.scalar,!1):os(t,n);case"enum":return n??t.enum.values[0].number}}set(t,n){if(tt(this.message,t),this.check){const r=De(t,n);if(r)throw r}let s;s=t.fieldKind=="message"?rs(t,n):Jn(n)||Xn(n)?n[Ie]:is(t,n),ea(this.message,t,s)}getUnknown(){return this.message.$unknown}setUnknown(t){this.message.$unknown=t}}function tt(e,t){if(t.parent.typeName!==e.$typeName)throw new z(t,`cannot use ${t.toString()} with message ${e.$typeName}`,"ForeignFieldError")}class Wo{field(){return this._field}get size(){return this._arr.length}constructor(t,n,s){this._field=t,this._arr=this[Ie]=n,this.check=s}get(t){const n=this._arr[t];return n===void 0?void 0:fn(this._field,n,this.check)}set(t,n){if(t<0||t>=this._arr.length)throw new z(this._field,`list item #${t+1}: out of range`);if(this.check){const s=Os(this._field,t,n);if(s)throw s}this._arr[t]=Ms(this._field,n)}add(t){if(this.check){const n=Os(this._field,this._arr.length,t);if(n)throw n}this._arr.push(Ms(this._field,t))}clear(){this._arr.splice(0,this._arr.length)}[Symbol.iterator](){return this.values()}keys(){return this._arr.keys()}*values(){for(const t of this._arr)yield fn(this._field,t,this.check)}*entries(){for(let t=0;t<this._arr.length;t++)yield[t,fn(this._field,this._arr[t],this.check)]}}class zo{constructor(t,n,s=!0){this.obj=this[Ie]=n??{},this.check=s,this._field=t}field(){return this._field}set(t,n){if(this.check){const s=function(r,a,o){const i=ua(a,r.mapKey);if(i!==!0)return new z(r,`invalid map key: ${qt({scalar:r.mapKey},a,i)}`);const l=ss(r,o);return l!==!0?new z(r,`map entry ${F(a)}: ${qt(r,o,l)}`):void 0}(this._field,t,n);if(s)throw s}return this.obj[Nt(t)]=function(s,r){return s.mapKind=="message"?rs(s,r):is(s,r)}(this._field,n),this}delete(t){const n=Nt(t),s=Object.prototype.hasOwnProperty.call(this.obj,n);return s&&delete this.obj[n],s}clear(){for(const t of Object.keys(this.obj))delete this.obj[t]}get(t){let n=this.obj[Nt(t)];return n!==void 0&&(n=yn(this._field,n,this.check)),n}has(t){return Object.prototype.hasOwnProperty.call(this.obj,Nt(t))}*keys(){for(const t of Object.keys(this.obj))yield Ds(t,this._field.mapKey)}*entries(){for(const t of Object.entries(this.obj))yield[Ds(t[0],this._field.mapKey),yn(this._field,t[1],this.check)]}[Symbol.iterator](){return this.entries()}get size(){return Object.keys(this.obj).length}*values(){for(const t of Object.values(this.obj))yield yn(this._field,t,this.check)}forEach(t,n){for(const s of this.entries())t.call(n,s[1],s[0],this)}}function rs(e,t){return Zn(t)?ta(t.message.$typeName)&&!e.oneof&&e.fieldKind=="message"?t.message.value:t.desc.typeName=="google.protobuf.Struct"&&e.parent.typeName!="google.protobuf.Value"?ga(t.message):t.message:t}function as(e,t,n){return t!==void 0&&(yt(e.message)&&!e.oneof&&e.fieldKind=="message"?t={$typeName:e.message.typeName,value:os(e.message.fields[0],t)}:e.message.typeName=="google.protobuf.Struct"&&e.parent.typeName!="google.protobuf.Value"&&Me(t)&&(t=pa(t))),new ha(e.message,t,n)}function Ms(e,t){return e.listKind=="message"?rs(e,t):is(e,t)}function fn(e,t,n){return e.listKind=="message"?as(e,t,n):os(e,t)}function yn(e,t,n){return e.mapKind=="message"?as(e,t,n):t}function Nt(e){return typeof e=="string"||typeof e=="number"?e:String(e)}function Ds(e,t){switch(t){case g.STRING:return e;case g.INT32:case g.FIXED32:case g.UINT32:case g.SFIXED32:case g.SINT32:{const n=Number.parseInt(e);if(Number.isFinite(n))return n;break}case g.BOOL:switch(e){case"true":return!0;case"false":return!1}break;case g.UINT64:case g.FIXED64:try{return k.uParse(e)}catch{}break;default:try{return k.parse(e)}catch{}}return e}function os(e,t){switch(e.scalar){case g.INT64:case g.SFIXED64:case g.SINT64:"longAsString"in e&&e.longAsString&&typeof t=="string"&&(t=k.parse(t));break;case g.FIXED64:case g.UINT64:"longAsString"in e&&e.longAsString&&typeof t=="string"&&(t=k.uParse(t))}return t}function is(e,t){switch(e.scalar){case g.INT64:case g.SFIXED64:case g.SINT64:"longAsString"in e&&e.longAsString?t=String(t):typeof t!="string"&&typeof t!="number"||(t=k.parse(t));break;case g.FIXED64:case g.UINT64:"longAsString"in e&&e.longAsString?t=String(t):typeof t!="string"&&typeof t!="number"||(t=k.uParse(t))}return t}function pa(e){const t={$typeName:"google.protobuf.Struct",fields:{}};if(Me(e))for(const[n,s]of Object.entries(e))t.fields[n]=ya(s);return t}function ga(e){const t={};for(const[n,s]of Object.entries(e.fields))t[n]=fa(s);return t}function fa(e){switch(e.kind.case){case"structValue":return ga(e.kind.value);case"listValue":return e.kind.value.values.map(fa);case"nullValue":case void 0:return null;default:return e.kind.value}}function ya(e){const t={$typeName:"google.protobuf.Value",kind:{case:void 0}};switch(typeof e){case"number":t.kind={case:"numberValue",value:e};break;case"string":t.kind={case:"stringValue",value:e};break;case"boolean":t.kind={case:"boolValue",value:e};break;case"object":if(e===null)t.kind={case:"nullValue",value:0};else if(Array.isArray(e)){const n={$typeName:"google.protobuf.ListValue",values:[]};if(Array.isArray(e))for(const s of e)n.values.push(ya(s));t.kind={case:"listValue",value:n}}else t.kind={case:"structValue",value:pa(e)}}return t}function _a(e){const t=function(){if(!Be){Be=[];const l=ba("std");for(let u=0;u<l.length;u++)Be[l[u].charCodeAt(0)]=u;Be[45]=l.indexOf("+"),Be[95]=l.indexOf("/")}return Be}();let n=3*e.length/4;e[e.length-2]=="="?n-=2:e[e.length-1]=="="&&(n-=1);let s,r=new Uint8Array(n),a=0,o=0,i=0;for(let l=0;l<e.length;l++){if(s=t[e.charCodeAt(l)],s===void 0)switch(e[l]){case"=":o=0;case`
`:case"\r":case"	":case" ":continue;default:throw Error("invalid base64 string")}switch(o){case 0:i=s,o=1;break;case 1:r[a++]=i<<2|(48&s)>>4,i=s,o=2;break;case 2:r[a++]=(15&i)<<4|(60&s)>>2,i=s,o=3;break;case 3:r[a++]=(3&i)<<6|s,o=0}}if(o==1)throw Error("invalid base64 string");return r.subarray(0,a)}let wt,$s,Be;function ba(e){return wt||(wt="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split(""),$s=wt.slice(0,-2).concat("-","_")),e=="url"?$s:wt}function ht(e){let t=!1;const n=[];for(let s=0;s<e.length;s++){let r=e.charAt(s);switch(r){case"_":t=!0;break;case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":n.push(r),t=!1;break;default:t&&(t=!1,r=r.toUpperCase()),n.push(r)}}return n.join("")}const Xo=new Set(["constructor","toString","toJSON","valueOf"]);function pt(e){return Xo.has(e)?e+"$":e}function ls(e){for(const t of e.field)ot(t,"jsonName")||(t.jsonName=ht(t.name));e.nestedType.forEach(ls)}function Jo(e,t){switch(e){case g.STRING:return t;case g.BYTES:{const n=function(s){const r=[],a={tail:s,c:"",next(){return this.tail.length!=0&&(this.c=this.tail[0],this.tail=this.tail.substring(1),!0)},take(o){if(this.tail.length>=o){const i=this.tail.substring(0,o);return this.tail=this.tail.substring(o),i}return!1}};for(;a.next();)if(a.c==="\\"){if(a.next())switch(a.c){case"\\":r.push(a.c.charCodeAt(0));break;case"b":r.push(8);break;case"f":r.push(12);break;case"n":r.push(10);break;case"r":r.push(13);break;case"t":r.push(9);break;case"v":r.push(11);break;case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":{const o=a.c,i=a.take(2);if(i===!1)return!1;const l=parseInt(o+i,8);if(Number.isNaN(l))return!1;r.push(l);break}case"x":{const o=a.c,i=a.take(2);if(i===!1)return!1;const l=parseInt(o+i,16);if(Number.isNaN(l))return!1;r.push(l);break}case"u":{const o=a.c,i=a.take(4);if(i===!1)return!1;const l=parseInt(o+i,16);if(Number.isNaN(l))return!1;const u=new Uint8Array(4);new DataView(u.buffer).setInt32(0,l,!0),r.push(u[0],u[1],u[2],u[3]);break}case"U":{const o=a.c,i=a.take(8);if(i===!1)return!1;const l=k.uEnc(o+i),u=new Uint8Array(8),d=new DataView(u.buffer);d.setInt32(0,l.lo,!0),d.setInt32(4,l.hi,!0),r.push(u[0],u[1],u[2],u[3],u[4],u[5],u[6],u[7]);break}}}else r.push(a.c.charCodeAt(0));return new Uint8Array(r)}(t);if(n===!1)throw new Error(`cannot parse ${g[e]} default value: ${t}`);return n}case g.INT64:case g.SFIXED64:case g.SINT64:return k.parse(t);case g.UINT64:case g.FIXED64:return k.uParse(t);case g.DOUBLE:case g.FLOAT:switch(t){case"inf":return Number.POSITIVE_INFINITY;case"-inf":return Number.NEGATIVE_INFINITY;case"nan":return Number.NaN;default:return parseFloat(t)}case g.BOOL:return t==="true";case g.INT32:case g.UINT32:case g.SINT32:case g.FIXED32:case g.SFIXED32:return parseInt(t,10)}}function*$n(e){switch(e.kind){case"file":for(const t of e.messages)yield t,yield*$n(t);yield*e.enums,yield*e.services,yield*e.extensions;break;case"message":for(const t of e.nestedMessages)yield t,yield*$n(t);yield*e.nestedEnums,yield*e.nestedExtensions}}function va(...e){const t=function(){const n=new Map,s=new Map,r=new Map;return{kind:"registry",types:n,extendees:s,[Symbol.iterator]:()=>n.values(),get files(){return r.values()},addFile(a,o,i){if(r.set(a.proto.name,a),!o)for(const l of $n(a))this.add(l);if(i)for(const l of a.dependencies)this.addFile(l,o,i)},add(a){if(a.kind=="extension"){let o=s.get(a.extendee.typeName);o||s.set(a.extendee.typeName,o=new Map),o.set(a.number,a)}n.set(a.typeName,a)},get:a=>n.get(a),getFile:a=>r.get(a),getMessage(a){const o=n.get(a);return(o==null?void 0:o.kind)=="message"?o:void 0},getEnum(a){const o=n.get(a);return(o==null?void 0:o.kind)=="enum"?o:void 0},getExtension(a){const o=n.get(a);return(o==null?void 0:o.kind)=="extension"?o:void 0},getExtensionFor(a,o){var i;return(i=s.get(a.typeName))===null||i===void 0?void 0:i.get(o)},getService(a){const o=n.get(a);return(o==null?void 0:o.kind)=="service"?o:void 0}}}();if(!e.length)return t;if("$typeName"in e[0]&&e[0].$typeName=="google.protobuf.FileDescriptorSet"){for(const n of e[0].file)Ps(n,t);return t}if("$typeName"in e[0]){let a=function(o){const i=[];for(const l of o.dependency){if(t.getFile(l)!=null||r.has(l))continue;const u=s(l);if(!u)throw new Error(`Unable to resolve ${l}, imported by ${o.name}`);"kind"in u?t.addFile(u,!1,!0):(r.add(u.name),i.push(u))}return i.concat(...i.map(a))};const n=e[0],s=e[1],r=new Set;for(const o of[n,...a(n)].reverse())Ps(o,t)}else for(const n of e)for(const s of n.files)t.addFile(s);return t}const Zo=998,Qo=999,ei=9,Ot=10,rt=11,ti=12,Fs=14,Fn=3,ni=2,Us=1,si=0,ri=1,ai=2,oi=3,ii=1,li=2,ui=1,Ea={998:{fieldPresence:1,enumType:2,repeatedFieldEncoding:2,utf8Validation:3,messageEncoding:1,jsonFormat:2,enforceNamingStyle:2},999:{fieldPresence:2,enumType:1,repeatedFieldEncoding:1,utf8Validation:2,messageEncoding:1,jsonFormat:1,enforceNamingStyle:2},1e3:{fieldPresence:1,enumType:1,repeatedFieldEncoding:1,utf8Validation:2,messageEncoding:1,jsonFormat:1,enforceNamingStyle:2}};function Ps(e,t){var n,s;const r={kind:"file",proto:e,deprecated:(s=(n=e.options)===null||n===void 0?void 0:n.deprecated)!==null&&s!==void 0&&s,edition:mi(e),name:e.name.replace(/\.proto$/,""),dependencies:hi(e,t),enums:[],messages:[],extensions:[],services:[],toString:()=>`file ${e.name}`},a=new Map,o={get:i=>a.get(i),add(i){var l;ge(((l=i.proto.options)===null||l===void 0?void 0:l.mapEntry)===!0),a.set(i.typeName,i)}};for(const i of e.enumType)Ta(i,r,void 0,t);for(const i of e.messageType)Sa(i,r,void 0,t,o);for(const i of e.service)ci(i,r,t);Un(r,t);for(const i of a.values())Pn(i,t,o);for(const i of r.messages)Pn(i,t,o),Un(i,t);t.addFile(r,!0)}function Un(e,t){switch(e.kind){case"file":for(const n of e.proto.extension){const s=Ln(n,e,t);e.extensions.push(s),t.add(s)}break;case"message":for(const n of e.proto.extension){const s=Ln(n,e,t);e.nestedExtensions.push(s),t.add(s)}for(const n of e.nestedMessages)Un(n,t)}}function Pn(e,t,n){const s=e.proto.oneofDecl.map(a=>function(o,i){return{kind:"oneof",proto:o,deprecated:!1,parent:i,fields:[],name:o.name,localName:pt(ht(o.name)),toString(){return`oneof ${i.typeName}.${this.name}`}}}(a,e)),r=new Set;for(const a of e.proto.field){const o=pi(a,s),i=Ln(a,e,t,o,n);e.fields.push(i),e.field[i.localName]=i,o===void 0?e.members.push(i):(o.fields.push(i),r.has(o)||(r.add(o),e.members.push(o)))}for(const a of s.filter(o=>r.has(o)))e.oneofs.push(a);for(const a of e.nestedMessages)Pn(a,t,n)}function Ta(e,t,n,s){var r,a,o,i,l;const u=function(m,h){const p=(f=m,(f.substring(0,1)+f.substring(1).replace(/[A-Z]/g,b=>"_"+b)).toLowerCase()+"_");var f;for(const b of h){if(!b.name.toLowerCase().startsWith(p))return;const _=b.name.substring(p.length);if(_.length==0||/^\d/.test(_))return}return p}(e.name,e.value),d={kind:"enum",proto:e,deprecated:(a=(r=e.options)===null||r===void 0?void 0:r.deprecated)!==null&&a!==void 0&&a,file:t,parent:n,open:!0,name:e.name,typeName:sn(e,n,t),value:{},values:[],sharedPrefix:u,toString(){return`enum ${this.typeName}`}};d.open=function(m){var h;return ui==Xe("enumType",{proto:m.proto,parent:(h=m.parent)!==null&&h!==void 0?h:m.file})}(d),s.add(d);for(const m of e.value){const h=m.name;d.values.push(d.value[m.number]={kind:"enum_value",proto:m,deprecated:(i=(o=e.options)===null||o===void 0?void 0:o.deprecated)!==null&&i!==void 0&&i,parent:d,name:h,localName:pt(u==null?h:h.substring(u.length)),number:m.number,toString:()=>`enum value ${d.typeName}.${h}`})}((l=n==null?void 0:n.nestedEnums)!==null&&l!==void 0?l:t.enums).push(d)}function Sa(e,t,n,s,r){var a,o,i,l;const u={kind:"message",proto:e,deprecated:(o=(a=e.options)===null||a===void 0?void 0:a.deprecated)!==null&&o!==void 0&&o,file:t,parent:n,name:e.name,typeName:sn(e,n,t),fields:[],field:{},oneofs:[],members:[],nestedEnums:[],nestedMessages:[],nestedExtensions:[],toString(){return`message ${this.typeName}`}};((i=e.options)===null||i===void 0?void 0:i.mapEntry)===!0?r.add(u):(((l=n==null?void 0:n.nestedMessages)!==null&&l!==void 0?l:t.messages).push(u),s.add(u));for(const d of e.enumType)Ta(d,t,u,s);for(const d of e.nestedType)Sa(d,t,u,s,r)}function ci(e,t,n){var s,r;const a={kind:"service",proto:e,deprecated:(r=(s=e.options)===null||s===void 0?void 0:s.deprecated)!==null&&r!==void 0&&r,file:t,name:e.name,typeName:sn(e,void 0,t),methods:[],method:{},toString(){return`service ${this.typeName}`}};t.services.push(a),n.add(a);for(const o of e.method){const i=di(o,a,n);a.methods.push(i),a.method[i.localName]=i}}function di(e,t,n){var s,r,a,o;let i;i=e.clientStreaming&&e.serverStreaming?"bidi_streaming":e.clientStreaming?"client_streaming":e.serverStreaming?"server_streaming":"unary";const l=n.getMessage(Se(e.inputType)),u=n.getMessage(Se(e.outputType));ge(l,`invalid MethodDescriptorProto: input_type ${e.inputType} not found`),ge(u,`invalid MethodDescriptorProto: output_type ${e.inputType} not found`);const d=e.name;return{kind:"rpc",proto:e,deprecated:(r=(s=e.options)===null||s===void 0?void 0:s.deprecated)!==null&&r!==void 0&&r,parent:t,name:d,localName:pt(d.length?pt(d[0].toLowerCase()+d.substring(1)):d),methodKind:i,input:l,output:u,idempotency:(o=(a=e.options)===null||a===void 0?void 0:a.idempotencyLevel)!==null&&o!==void 0?o:si,toString:()=>`rpc ${t.typeName}.${d}`}}function Ln(e,t,n,s,r){var a,o,i;const l=r===void 0,u={kind:"field",proto:e,deprecated:(o=(a=e.options)===null||a===void 0?void 0:a.deprecated)!==null&&o!==void 0&&o,name:e.name,number:e.number,scalar:void 0,message:void 0,enum:void 0,presence:gi(e,s,l,t),listKind:void 0,mapKind:void 0,mapKey:void 0,delimitedEncoding:void 0,packed:void 0,longAsString:!1,getDefaultValue:void 0};if(l){const p=t.kind=="file"?t:t.file,f=t.kind=="file"?void 0:t,b=sn(e,f,p);u.kind="extension",u.file=p,u.parent=f,u.oneof=void 0,u.typeName=b,u.jsonName=`[${b}]`,u.toString=()=>`extension ${b}`;const _=n.getMessage(Se(e.extendee));ge(_,`invalid FieldDescriptorProto: extendee ${e.extendee} not found`),u.extendee=_}else{const p=t;ge(p.kind=="message"),u.parent=p,u.oneof=s,u.localName=s?ht(e.name):pt(ht(e.name)),u.jsonName=e.jsonName,u.toString=()=>`field ${p.typeName}.${e.name}`}const d=e.label,m=e.type,h=(i=e.options)===null||i===void 0?void 0:i.jstype;if(d===Fn){const p=m==rt?r==null?void 0:r.get(Se(e.typeName)):void 0;if(p){u.fieldKind="map";const{key:f,value:b}=function(_){const E=_.fields.find(y=>y.number===1),C=_.fields.find(y=>y.number===2);return ge(E&&E.fieldKind=="scalar"&&E.scalar!=g.BYTES&&E.scalar!=g.FLOAT&&E.scalar!=g.DOUBLE&&C&&C.fieldKind!="list"&&C.fieldKind!="map"),{key:E,value:C}}(p);return u.mapKey=f.scalar,u.mapKind=b.fieldKind,u.message=b.message,u.delimitedEncoding=!1,u.enum=b.enum,u.scalar=b.scalar,u}switch(u.fieldKind="list",m){case rt:case Ot:u.listKind="message",u.message=n.getMessage(Se(e.typeName)),ge(u.message),u.delimitedEncoding=Ls(e,t);break;case Fs:u.listKind="enum",u.enum=n.getEnum(Se(e.typeName)),ge(u.enum);break;default:u.listKind="scalar",u.scalar=m,u.longAsString=h==Us}return u.packed=function(f,b){if(f.label!=Fn)return!1;switch(f.type){case ei:case ti:case Ot:case rt:return!1}const _=f.options;return _&&ot(_,"packed")?_.packed:ii==Xe("repeatedFieldEncoding",{proto:f,parent:b})}(e,t),u}switch(m){case rt:case Ot:u.fieldKind="message",u.message=n.getMessage(Se(e.typeName)),ge(u.message,`invalid FieldDescriptorProto: type_name ${e.typeName} not found`),u.delimitedEncoding=Ls(e,t),u.getDefaultValue=()=>{};break;case Fs:{const p=n.getEnum(Se(e.typeName));ge(p!==void 0,`invalid FieldDescriptorProto: type_name ${e.typeName} not found`),u.fieldKind="enum",u.enum=n.getEnum(Se(e.typeName)),u.getDefaultValue=()=>ot(e,"defaultValue")?function(f,b){const _=f.values.find(E=>E.name===b);if(!_)throw new Error(`cannot parse ${f} default value: ${b}`);return _.number}(p,e.defaultValue):void 0;break}default:u.fieldKind="scalar",u.scalar=m,u.longAsString=h==Us,u.getDefaultValue=()=>ot(e,"defaultValue")?Jo(m,e.defaultValue):void 0}return u}function mi(e){switch(e.syntax){case"":case"proto2":return Zo;case"proto3":return Qo;case"editions":if(e.edition in Ea)return e.edition;throw new Error(`${e.name}: unsupported edition`);default:throw new Error(`${e.name}: unsupported syntax "${e.syntax}"`)}}function hi(e,t){return e.dependency.map(n=>{const s=t.getFile(n);if(!s)throw new Error(`Cannot find ${n}, imported by ${e.name}`);return s})}function sn(e,t,n){let s;return s=t?`${t.typeName}.${e.name}`:n.proto.package.length>0?`${n.proto.package}.${e.name}`:`${e.name}`,s}function Se(e){return e.startsWith(".")?e.substring(1):e}function pi(e,t){if(!ot(e,"oneofIndex")||e.proto3Optional)return;const n=t[e.oneofIndex];return ge(n,`invalid FieldDescriptorProto: oneof #${e.oneofIndex} for field #${e.number} not found`),n}function gi(e,t,n,s){return e.label==ni?oi:e.label==Fn?ai:t||e.proto3Optional||e.type==rt||n?ri:Xe("fieldPresence",{proto:e,parent:s})}function Ls(e,t){return e.type==Ot||li==Xe("messageEncoding",{proto:e,parent:t})}function Xe(e,t){var n,s;const r=(n=t.proto.options)===null||n===void 0?void 0:n.features;if(r){const a=r[e];if(a!=0)return a}if("kind"in t){if(t.kind=="message")return Xe(e,(s=t.parent)!==null&&s!==void 0?s:t.file);const a=Ea[t.edition];if(!a)throw new Error(`feature default for edition ${t.edition} not found`);return a[e]}return Xe(e,t.parent)}function ge(e,t){if(!e)throw new Error(t)}function fi(e){const t=function(n){return Object.assign(Object.create({syntax:"",edition:0}),Object.assign(Object.assign({$typeName:"google.protobuf.FileDescriptorProto",dependency:[],publicDependency:[],weakDependency:[],service:[],extension:[]},n),{messageType:n.messageType.map(Ia),enumType:n.enumType.map(Na)}))}(e);return t.messageType.forEach(ls),va(t,()=>{}).getFile(t.name)}function Ia(e){var t,n,s,r,a,o,i,l;return{$typeName:"google.protobuf.DescriptorProto",name:e.name,field:(n=(t=e.field)===null||t===void 0?void 0:t.map(yi))!==null&&n!==void 0?n:[],extension:[],nestedType:(r=(s=e.nestedType)===null||s===void 0?void 0:s.map(Ia))!==null&&r!==void 0?r:[],enumType:(o=(a=e.enumType)===null||a===void 0?void 0:a.map(Na))!==null&&o!==void 0?o:[],extensionRange:(l=(i=e.extensionRange)===null||i===void 0?void 0:i.map(u=>Object.assign({$typeName:"google.protobuf.DescriptorProto.ExtensionRange"},u)))!==null&&l!==void 0?l:[],oneofDecl:[],reservedRange:[],reservedName:[]}}function yi(e){return Object.assign(Object.create({label:1,typeName:"",extendee:"",defaultValue:"",oneofIndex:0,jsonName:"",proto3Optional:!1}),Object.assign(Object.assign({$typeName:"google.protobuf.FieldDescriptorProto"},e),{options:e.options?_i(e.options):void 0}))}function _i(e){var t,n,s;return Object.assign(Object.create({ctype:0,packed:!1,jstype:0,lazy:!1,unverifiedLazy:!1,deprecated:!1,weak:!1,debugRedact:!1,retention:0}),Object.assign(Object.assign({$typeName:"google.protobuf.FieldOptions"},e),{targets:(t=e.targets)!==null&&t!==void 0?t:[],editionDefaults:(s=(n=e.editionDefaults)===null||n===void 0?void 0:n.map(a=>Object.assign({$typeName:"google.protobuf.FieldOptions.EditionDefault"},a)))!==null&&s!==void 0?s:[],uninterpretedOption:[]}))}function Na(e){return{$typeName:"google.protobuf.EnumDescriptorProto",name:e.name,reservedName:[],reservedRange:[],value:e.value.map(t=>Object.assign({$typeName:"google.protobuf.EnumValueDescriptorProto"},t))}}function _t(e,t,...n){return n.reduce((s,r)=>s.nestedMessages[r],e.messages[t])}const bi=_t(fi({name:"google/protobuf/descriptor.proto",package:"google.protobuf",messageType:[{name:"FileDescriptorSet",field:[{name:"file",number:1,type:11,label:3,typeName:".google.protobuf.FileDescriptorProto"}],extensionRange:[{start:536e6,end:536000001}]},{name:"FileDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"package",number:2,type:9,label:1},{name:"dependency",number:3,type:9,label:3},{name:"public_dependency",number:10,type:5,label:3},{name:"weak_dependency",number:11,type:5,label:3},{name:"message_type",number:4,type:11,label:3,typeName:".google.protobuf.DescriptorProto"},{name:"enum_type",number:5,type:11,label:3,typeName:".google.protobuf.EnumDescriptorProto"},{name:"service",number:6,type:11,label:3,typeName:".google.protobuf.ServiceDescriptorProto"},{name:"extension",number:7,type:11,label:3,typeName:".google.protobuf.FieldDescriptorProto"},{name:"options",number:8,type:11,label:1,typeName:".google.protobuf.FileOptions"},{name:"source_code_info",number:9,type:11,label:1,typeName:".google.protobuf.SourceCodeInfo"},{name:"syntax",number:12,type:9,label:1},{name:"edition",number:14,type:14,label:1,typeName:".google.protobuf.Edition"}]},{name:"DescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"field",number:2,type:11,label:3,typeName:".google.protobuf.FieldDescriptorProto"},{name:"extension",number:6,type:11,label:3,typeName:".google.protobuf.FieldDescriptorProto"},{name:"nested_type",number:3,type:11,label:3,typeName:".google.protobuf.DescriptorProto"},{name:"enum_type",number:4,type:11,label:3,typeName:".google.protobuf.EnumDescriptorProto"},{name:"extension_range",number:5,type:11,label:3,typeName:".google.protobuf.DescriptorProto.ExtensionRange"},{name:"oneof_decl",number:8,type:11,label:3,typeName:".google.protobuf.OneofDescriptorProto"},{name:"options",number:7,type:11,label:1,typeName:".google.protobuf.MessageOptions"},{name:"reserved_range",number:9,type:11,label:3,typeName:".google.protobuf.DescriptorProto.ReservedRange"},{name:"reserved_name",number:10,type:9,label:3}],nestedType:[{name:"ExtensionRange",field:[{name:"start",number:1,type:5,label:1},{name:"end",number:2,type:5,label:1},{name:"options",number:3,type:11,label:1,typeName:".google.protobuf.ExtensionRangeOptions"}]},{name:"ReservedRange",field:[{name:"start",number:1,type:5,label:1},{name:"end",number:2,type:5,label:1}]}]},{name:"ExtensionRangeOptions",field:[{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"},{name:"declaration",number:2,type:11,label:3,typeName:".google.protobuf.ExtensionRangeOptions.Declaration",options:{retention:2}},{name:"features",number:50,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"verification",number:3,type:14,label:1,typeName:".google.protobuf.ExtensionRangeOptions.VerificationState",defaultValue:"UNVERIFIED",options:{retention:2}}],nestedType:[{name:"Declaration",field:[{name:"number",number:1,type:5,label:1},{name:"full_name",number:2,type:9,label:1},{name:"type",number:3,type:9,label:1},{name:"reserved",number:5,type:8,label:1},{name:"repeated",number:6,type:8,label:1}]}],enumType:[{name:"VerificationState",value:[{name:"DECLARATION",number:0},{name:"UNVERIFIED",number:1}]}],extensionRange:[{start:1e3,end:536870912}]},{name:"FieldDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"number",number:3,type:5,label:1},{name:"label",number:4,type:14,label:1,typeName:".google.protobuf.FieldDescriptorProto.Label"},{name:"type",number:5,type:14,label:1,typeName:".google.protobuf.FieldDescriptorProto.Type"},{name:"type_name",number:6,type:9,label:1},{name:"extendee",number:2,type:9,label:1},{name:"default_value",number:7,type:9,label:1},{name:"oneof_index",number:9,type:5,label:1},{name:"json_name",number:10,type:9,label:1},{name:"options",number:8,type:11,label:1,typeName:".google.protobuf.FieldOptions"},{name:"proto3_optional",number:17,type:8,label:1}],enumType:[{name:"Type",value:[{name:"TYPE_DOUBLE",number:1},{name:"TYPE_FLOAT",number:2},{name:"TYPE_INT64",number:3},{name:"TYPE_UINT64",number:4},{name:"TYPE_INT32",number:5},{name:"TYPE_FIXED64",number:6},{name:"TYPE_FIXED32",number:7},{name:"TYPE_BOOL",number:8},{name:"TYPE_STRING",number:9},{name:"TYPE_GROUP",number:10},{name:"TYPE_MESSAGE",number:11},{name:"TYPE_BYTES",number:12},{name:"TYPE_UINT32",number:13},{name:"TYPE_ENUM",number:14},{name:"TYPE_SFIXED32",number:15},{name:"TYPE_SFIXED64",number:16},{name:"TYPE_SINT32",number:17},{name:"TYPE_SINT64",number:18}]},{name:"Label",value:[{name:"LABEL_OPTIONAL",number:1},{name:"LABEL_REPEATED",number:3},{name:"LABEL_REQUIRED",number:2}]}]},{name:"OneofDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"options",number:2,type:11,label:1,typeName:".google.protobuf.OneofOptions"}]},{name:"EnumDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"value",number:2,type:11,label:3,typeName:".google.protobuf.EnumValueDescriptorProto"},{name:"options",number:3,type:11,label:1,typeName:".google.protobuf.EnumOptions"},{name:"reserved_range",number:4,type:11,label:3,typeName:".google.protobuf.EnumDescriptorProto.EnumReservedRange"},{name:"reserved_name",number:5,type:9,label:3}],nestedType:[{name:"EnumReservedRange",field:[{name:"start",number:1,type:5,label:1},{name:"end",number:2,type:5,label:1}]}]},{name:"EnumValueDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"number",number:2,type:5,label:1},{name:"options",number:3,type:11,label:1,typeName:".google.protobuf.EnumValueOptions"}]},{name:"ServiceDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"method",number:2,type:11,label:3,typeName:".google.protobuf.MethodDescriptorProto"},{name:"options",number:3,type:11,label:1,typeName:".google.protobuf.ServiceOptions"}]},{name:"MethodDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"input_type",number:2,type:9,label:1},{name:"output_type",number:3,type:9,label:1},{name:"options",number:4,type:11,label:1,typeName:".google.protobuf.MethodOptions"},{name:"client_streaming",number:5,type:8,label:1,defaultValue:"false"},{name:"server_streaming",number:6,type:8,label:1,defaultValue:"false"}]},{name:"FileOptions",field:[{name:"java_package",number:1,type:9,label:1},{name:"java_outer_classname",number:8,type:9,label:1},{name:"java_multiple_files",number:10,type:8,label:1,defaultValue:"false"},{name:"java_generate_equals_and_hash",number:20,type:8,label:1,options:{deprecated:!0}},{name:"java_string_check_utf8",number:27,type:8,label:1,defaultValue:"false"},{name:"optimize_for",number:9,type:14,label:1,typeName:".google.protobuf.FileOptions.OptimizeMode",defaultValue:"SPEED"},{name:"go_package",number:11,type:9,label:1},{name:"cc_generic_services",number:16,type:8,label:1,defaultValue:"false"},{name:"java_generic_services",number:17,type:8,label:1,defaultValue:"false"},{name:"py_generic_services",number:18,type:8,label:1,defaultValue:"false"},{name:"deprecated",number:23,type:8,label:1,defaultValue:"false"},{name:"cc_enable_arenas",number:31,type:8,label:1,defaultValue:"true"},{name:"objc_class_prefix",number:36,type:9,label:1},{name:"csharp_namespace",number:37,type:9,label:1},{name:"swift_prefix",number:39,type:9,label:1},{name:"php_class_prefix",number:40,type:9,label:1},{name:"php_namespace",number:41,type:9,label:1},{name:"php_metadata_namespace",number:44,type:9,label:1},{name:"ruby_package",number:45,type:9,label:1},{name:"features",number:50,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],enumType:[{name:"OptimizeMode",value:[{name:"SPEED",number:1},{name:"CODE_SIZE",number:2},{name:"LITE_RUNTIME",number:3}]}],extensionRange:[{start:1e3,end:536870912}]},{name:"MessageOptions",field:[{name:"message_set_wire_format",number:1,type:8,label:1,defaultValue:"false"},{name:"no_standard_descriptor_accessor",number:2,type:8,label:1,defaultValue:"false"},{name:"deprecated",number:3,type:8,label:1,defaultValue:"false"},{name:"map_entry",number:7,type:8,label:1},{name:"deprecated_legacy_json_field_conflicts",number:11,type:8,label:1,options:{deprecated:!0}},{name:"features",number:12,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],extensionRange:[{start:1e3,end:536870912}]},{name:"FieldOptions",field:[{name:"ctype",number:1,type:14,label:1,typeName:".google.protobuf.FieldOptions.CType",defaultValue:"STRING"},{name:"packed",number:2,type:8,label:1},{name:"jstype",number:6,type:14,label:1,typeName:".google.protobuf.FieldOptions.JSType",defaultValue:"JS_NORMAL"},{name:"lazy",number:5,type:8,label:1,defaultValue:"false"},{name:"unverified_lazy",number:15,type:8,label:1,defaultValue:"false"},{name:"deprecated",number:3,type:8,label:1,defaultValue:"false"},{name:"weak",number:10,type:8,label:1,defaultValue:"false"},{name:"debug_redact",number:16,type:8,label:1,defaultValue:"false"},{name:"retention",number:17,type:14,label:1,typeName:".google.protobuf.FieldOptions.OptionRetention"},{name:"targets",number:19,type:14,label:3,typeName:".google.protobuf.FieldOptions.OptionTargetType"},{name:"edition_defaults",number:20,type:11,label:3,typeName:".google.protobuf.FieldOptions.EditionDefault"},{name:"features",number:21,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"feature_support",number:22,type:11,label:1,typeName:".google.protobuf.FieldOptions.FeatureSupport"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],nestedType:[{name:"EditionDefault",field:[{name:"edition",number:3,type:14,label:1,typeName:".google.protobuf.Edition"},{name:"value",number:2,type:9,label:1}]},{name:"FeatureSupport",field:[{name:"edition_introduced",number:1,type:14,label:1,typeName:".google.protobuf.Edition"},{name:"edition_deprecated",number:2,type:14,label:1,typeName:".google.protobuf.Edition"},{name:"deprecation_warning",number:3,type:9,label:1},{name:"edition_removed",number:4,type:14,label:1,typeName:".google.protobuf.Edition"}]}],enumType:[{name:"CType",value:[{name:"STRING",number:0},{name:"CORD",number:1},{name:"STRING_PIECE",number:2}]},{name:"JSType",value:[{name:"JS_NORMAL",number:0},{name:"JS_STRING",number:1},{name:"JS_NUMBER",number:2}]},{name:"OptionRetention",value:[{name:"RETENTION_UNKNOWN",number:0},{name:"RETENTION_RUNTIME",number:1},{name:"RETENTION_SOURCE",number:2}]},{name:"OptionTargetType",value:[{name:"TARGET_TYPE_UNKNOWN",number:0},{name:"TARGET_TYPE_FILE",number:1},{name:"TARGET_TYPE_EXTENSION_RANGE",number:2},{name:"TARGET_TYPE_MESSAGE",number:3},{name:"TARGET_TYPE_FIELD",number:4},{name:"TARGET_TYPE_ONEOF",number:5},{name:"TARGET_TYPE_ENUM",number:6},{name:"TARGET_TYPE_ENUM_ENTRY",number:7},{name:"TARGET_TYPE_SERVICE",number:8},{name:"TARGET_TYPE_METHOD",number:9}]}],extensionRange:[{start:1e3,end:536870912}]},{name:"OneofOptions",field:[{name:"features",number:1,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],extensionRange:[{start:1e3,end:536870912}]},{name:"EnumOptions",field:[{name:"allow_alias",number:2,type:8,label:1},{name:"deprecated",number:3,type:8,label:1,defaultValue:"false"},{name:"deprecated_legacy_json_field_conflicts",number:6,type:8,label:1,options:{deprecated:!0}},{name:"features",number:7,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],extensionRange:[{start:1e3,end:536870912}]},{name:"EnumValueOptions",field:[{name:"deprecated",number:1,type:8,label:1,defaultValue:"false"},{name:"features",number:2,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"debug_redact",number:3,type:8,label:1,defaultValue:"false"},{name:"feature_support",number:4,type:11,label:1,typeName:".google.protobuf.FieldOptions.FeatureSupport"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],extensionRange:[{start:1e3,end:536870912}]},{name:"ServiceOptions",field:[{name:"features",number:34,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"deprecated",number:33,type:8,label:1,defaultValue:"false"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],extensionRange:[{start:1e3,end:536870912}]},{name:"MethodOptions",field:[{name:"deprecated",number:33,type:8,label:1,defaultValue:"false"},{name:"idempotency_level",number:34,type:14,label:1,typeName:".google.protobuf.MethodOptions.IdempotencyLevel",defaultValue:"IDEMPOTENCY_UNKNOWN"},{name:"features",number:35,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],enumType:[{name:"IdempotencyLevel",value:[{name:"IDEMPOTENCY_UNKNOWN",number:0},{name:"NO_SIDE_EFFECTS",number:1},{name:"IDEMPOTENT",number:2}]}],extensionRange:[{start:1e3,end:536870912}]},{name:"UninterpretedOption",field:[{name:"name",number:2,type:11,label:3,typeName:".google.protobuf.UninterpretedOption.NamePart"},{name:"identifier_value",number:3,type:9,label:1},{name:"positive_int_value",number:4,type:4,label:1},{name:"negative_int_value",number:5,type:3,label:1},{name:"double_value",number:6,type:1,label:1},{name:"string_value",number:7,type:12,label:1},{name:"aggregate_value",number:8,type:9,label:1}],nestedType:[{name:"NamePart",field:[{name:"name_part",number:1,type:9,label:2},{name:"is_extension",number:2,type:8,label:2}]}]},{name:"FeatureSet",field:[{name:"field_presence",number:1,type:14,label:1,typeName:".google.protobuf.FeatureSet.FieldPresence",options:{retention:1,targets:[4,1],editionDefaults:[{value:"EXPLICIT",edition:900},{value:"IMPLICIT",edition:999},{value:"EXPLICIT",edition:1e3}]}},{name:"enum_type",number:2,type:14,label:1,typeName:".google.protobuf.FeatureSet.EnumType",options:{retention:1,targets:[6,1],editionDefaults:[{value:"CLOSED",edition:900},{value:"OPEN",edition:999}]}},{name:"repeated_field_encoding",number:3,type:14,label:1,typeName:".google.protobuf.FeatureSet.RepeatedFieldEncoding",options:{retention:1,targets:[4,1],editionDefaults:[{value:"EXPANDED",edition:900},{value:"PACKED",edition:999}]}},{name:"utf8_validation",number:4,type:14,label:1,typeName:".google.protobuf.FeatureSet.Utf8Validation",options:{retention:1,targets:[4,1],editionDefaults:[{value:"NONE",edition:900},{value:"VERIFY",edition:999}]}},{name:"message_encoding",number:5,type:14,label:1,typeName:".google.protobuf.FeatureSet.MessageEncoding",options:{retention:1,targets:[4,1],editionDefaults:[{value:"LENGTH_PREFIXED",edition:900}]}},{name:"json_format",number:6,type:14,label:1,typeName:".google.protobuf.FeatureSet.JsonFormat",options:{retention:1,targets:[3,6,1],editionDefaults:[{value:"LEGACY_BEST_EFFORT",edition:900},{value:"ALLOW",edition:999}]}},{name:"enforce_naming_style",number:7,type:14,label:1,typeName:".google.protobuf.FeatureSet.EnforceNamingStyle",options:{retention:2,targets:[1,2,3,4,5,6,7,8,9],editionDefaults:[{value:"STYLE_LEGACY",edition:900},{value:"STYLE2024",edition:1001}]}}],enumType:[{name:"FieldPresence",value:[{name:"FIELD_PRESENCE_UNKNOWN",number:0},{name:"EXPLICIT",number:1},{name:"IMPLICIT",number:2},{name:"LEGACY_REQUIRED",number:3}]},{name:"EnumType",value:[{name:"ENUM_TYPE_UNKNOWN",number:0},{name:"OPEN",number:1},{name:"CLOSED",number:2}]},{name:"RepeatedFieldEncoding",value:[{name:"REPEATED_FIELD_ENCODING_UNKNOWN",number:0},{name:"PACKED",number:1},{name:"EXPANDED",number:2}]},{name:"Utf8Validation",value:[{name:"UTF8_VALIDATION_UNKNOWN",number:0},{name:"VERIFY",number:2},{name:"NONE",number:3}]},{name:"MessageEncoding",value:[{name:"MESSAGE_ENCODING_UNKNOWN",number:0},{name:"LENGTH_PREFIXED",number:1},{name:"DELIMITED",number:2}]},{name:"JsonFormat",value:[{name:"JSON_FORMAT_UNKNOWN",number:0},{name:"ALLOW",number:1},{name:"LEGACY_BEST_EFFORT",number:2}]},{name:"EnforceNamingStyle",value:[{name:"ENFORCE_NAMING_STYLE_UNKNOWN",number:0},{name:"STYLE2024",number:1},{name:"STYLE_LEGACY",number:2}]}],extensionRange:[{start:1e3,end:9995},{start:9995,end:1e4},{start:1e4,end:10001}]},{name:"FeatureSetDefaults",field:[{name:"defaults",number:1,type:11,label:3,typeName:".google.protobuf.FeatureSetDefaults.FeatureSetEditionDefault"},{name:"minimum_edition",number:4,type:14,label:1,typeName:".google.protobuf.Edition"},{name:"maximum_edition",number:5,type:14,label:1,typeName:".google.protobuf.Edition"}],nestedType:[{name:"FeatureSetEditionDefault",field:[{name:"edition",number:3,type:14,label:1,typeName:".google.protobuf.Edition"},{name:"overridable_features",number:4,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"fixed_features",number:5,type:11,label:1,typeName:".google.protobuf.FeatureSet"}]}]},{name:"SourceCodeInfo",field:[{name:"location",number:1,type:11,label:3,typeName:".google.protobuf.SourceCodeInfo.Location"}],nestedType:[{name:"Location",field:[{name:"path",number:1,type:5,label:3,options:{packed:!0}},{name:"span",number:2,type:5,label:3,options:{packed:!0}},{name:"leading_comments",number:3,type:9,label:1},{name:"trailing_comments",number:4,type:9,label:1},{name:"leading_detached_comments",number:6,type:9,label:3}]}],extensionRange:[{start:536e6,end:536000001}]},{name:"GeneratedCodeInfo",field:[{name:"annotation",number:1,type:11,label:3,typeName:".google.protobuf.GeneratedCodeInfo.Annotation"}],nestedType:[{name:"Annotation",field:[{name:"path",number:1,type:5,label:3,options:{packed:!0}},{name:"source_file",number:2,type:9,label:1},{name:"begin",number:3,type:5,label:1},{name:"end",number:4,type:5,label:1},{name:"semantic",number:5,type:14,label:1,typeName:".google.protobuf.GeneratedCodeInfo.Annotation.Semantic"}],enumType:[{name:"Semantic",value:[{name:"NONE",number:0},{name:"SET",number:1},{name:"ALIAS",number:2}]}]}]}],enumType:[{name:"Edition",value:[{name:"EDITION_UNKNOWN",number:0},{name:"EDITION_LEGACY",number:900},{name:"EDITION_PROTO2",number:998},{name:"EDITION_PROTO3",number:999},{name:"EDITION_2023",number:1e3},{name:"EDITION_2024",number:1001},{name:"EDITION_1_TEST_ONLY",number:1},{name:"EDITION_2_TEST_ONLY",number:2},{name:"EDITION_99997_TEST_ONLY",number:99997},{name:"EDITION_99998_TEST_ONLY",number:99998},{name:"EDITION_99999_TEST_ONLY",number:99999},{name:"EDITION_MAX",number:2147483647}]}]}),1);var qs,Hs,Gs,Vs,Bs,Ys,js,Ks,Ws,zs,Xs,Js,Zs,Qs,er,tr,nr,sr;(function(e){e[e.DECLARATION=0]="DECLARATION",e[e.UNVERIFIED=1]="UNVERIFIED"})(qs||(qs={})),function(e){e[e.DOUBLE=1]="DOUBLE",e[e.FLOAT=2]="FLOAT",e[e.INT64=3]="INT64",e[e.UINT64=4]="UINT64",e[e.INT32=5]="INT32",e[e.FIXED64=6]="FIXED64",e[e.FIXED32=7]="FIXED32",e[e.BOOL=8]="BOOL",e[e.STRING=9]="STRING",e[e.GROUP=10]="GROUP",e[e.MESSAGE=11]="MESSAGE",e[e.BYTES=12]="BYTES",e[e.UINT32=13]="UINT32",e[e.ENUM=14]="ENUM",e[e.SFIXED32=15]="SFIXED32",e[e.SFIXED64=16]="SFIXED64",e[e.SINT32=17]="SINT32",e[e.SINT64=18]="SINT64"}(Hs||(Hs={})),function(e){e[e.OPTIONAL=1]="OPTIONAL",e[e.REPEATED=3]="REPEATED",e[e.REQUIRED=2]="REQUIRED"}(Gs||(Gs={})),function(e){e[e.SPEED=1]="SPEED",e[e.CODE_SIZE=2]="CODE_SIZE",e[e.LITE_RUNTIME=3]="LITE_RUNTIME"}(Vs||(Vs={})),function(e){e[e.STRING=0]="STRING",e[e.CORD=1]="CORD",e[e.STRING_PIECE=2]="STRING_PIECE"}(Bs||(Bs={})),function(e){e[e.JS_NORMAL=0]="JS_NORMAL",e[e.JS_STRING=1]="JS_STRING",e[e.JS_NUMBER=2]="JS_NUMBER"}(Ys||(Ys={})),function(e){e[e.RETENTION_UNKNOWN=0]="RETENTION_UNKNOWN",e[e.RETENTION_RUNTIME=1]="RETENTION_RUNTIME",e[e.RETENTION_SOURCE=2]="RETENTION_SOURCE"}(js||(js={})),function(e){e[e.TARGET_TYPE_UNKNOWN=0]="TARGET_TYPE_UNKNOWN",e[e.TARGET_TYPE_FILE=1]="TARGET_TYPE_FILE",e[e.TARGET_TYPE_EXTENSION_RANGE=2]="TARGET_TYPE_EXTENSION_RANGE",e[e.TARGET_TYPE_MESSAGE=3]="TARGET_TYPE_MESSAGE",e[e.TARGET_TYPE_FIELD=4]="TARGET_TYPE_FIELD",e[e.TARGET_TYPE_ONEOF=5]="TARGET_TYPE_ONEOF",e[e.TARGET_TYPE_ENUM=6]="TARGET_TYPE_ENUM",e[e.TARGET_TYPE_ENUM_ENTRY=7]="TARGET_TYPE_ENUM_ENTRY",e[e.TARGET_TYPE_SERVICE=8]="TARGET_TYPE_SERVICE",e[e.TARGET_TYPE_METHOD=9]="TARGET_TYPE_METHOD"}(Ks||(Ks={})),function(e){e[e.IDEMPOTENCY_UNKNOWN=0]="IDEMPOTENCY_UNKNOWN",e[e.NO_SIDE_EFFECTS=1]="NO_SIDE_EFFECTS",e[e.IDEMPOTENT=2]="IDEMPOTENT"}(Ws||(Ws={})),function(e){e[e.FIELD_PRESENCE_UNKNOWN=0]="FIELD_PRESENCE_UNKNOWN",e[e.EXPLICIT=1]="EXPLICIT",e[e.IMPLICIT=2]="IMPLICIT",e[e.LEGACY_REQUIRED=3]="LEGACY_REQUIRED"}(zs||(zs={})),function(e){e[e.ENUM_TYPE_UNKNOWN=0]="ENUM_TYPE_UNKNOWN",e[e.OPEN=1]="OPEN",e[e.CLOSED=2]="CLOSED"}(Xs||(Xs={})),function(e){e[e.REPEATED_FIELD_ENCODING_UNKNOWN=0]="REPEATED_FIELD_ENCODING_UNKNOWN",e[e.PACKED=1]="PACKED",e[e.EXPANDED=2]="EXPANDED"}(Js||(Js={})),function(e){e[e.UTF8_VALIDATION_UNKNOWN=0]="UTF8_VALIDATION_UNKNOWN",e[e.VERIFY=2]="VERIFY",e[e.NONE=3]="NONE"}(Zs||(Zs={})),function(e){e[e.MESSAGE_ENCODING_UNKNOWN=0]="MESSAGE_ENCODING_UNKNOWN",e[e.LENGTH_PREFIXED=1]="LENGTH_PREFIXED",e[e.DELIMITED=2]="DELIMITED"}(Qs||(Qs={})),function(e){e[e.JSON_FORMAT_UNKNOWN=0]="JSON_FORMAT_UNKNOWN",e[e.ALLOW=1]="ALLOW",e[e.LEGACY_BEST_EFFORT=2]="LEGACY_BEST_EFFORT"}(er||(er={})),function(e){e[e.ENFORCE_NAMING_STYLE_UNKNOWN=0]="ENFORCE_NAMING_STYLE_UNKNOWN",e[e.STYLE2024=1]="STYLE2024",e[e.STYLE_LEGACY=2]="STYLE_LEGACY"}(tr||(tr={})),function(e){e[e.NONE=0]="NONE",e[e.SET=1]="SET",e[e.ALIAS=2]="ALIAS"}(nr||(nr={})),function(e){e[e.EDITION_UNKNOWN=0]="EDITION_UNKNOWN",e[e.EDITION_LEGACY=900]="EDITION_LEGACY",e[e.EDITION_PROTO2=998]="EDITION_PROTO2",e[e.EDITION_PROTO3=999]="EDITION_PROTO3",e[e.EDITION_2023=1e3]="EDITION_2023",e[e.EDITION_2024=1001]="EDITION_2024",e[e.EDITION_1_TEST_ONLY=1]="EDITION_1_TEST_ONLY",e[e.EDITION_2_TEST_ONLY=2]="EDITION_2_TEST_ONLY",e[e.EDITION_99997_TEST_ONLY=99997]="EDITION_99997_TEST_ONLY",e[e.EDITION_99998_TEST_ONLY=99998]="EDITION_99998_TEST_ONLY",e[e.EDITION_99999_TEST_ONLY=99999]="EDITION_99999_TEST_ONLY",e[e.EDITION_MAX=2147483647]="EDITION_MAX"}(sr||(sr={}));const vi={readUnknownFields:!0};function us(e,t,n){const s=ie(e,void 0,!1);return wa(s,new ns(t),vi,!1,t.byteLength),s.message}function wa(e,t,n,s,r){var a;const o=s?t.len:t.pos+r;let i,l;const u=(a=e.getUnknown())!==null&&a!==void 0?a:[];for(;t.pos<o&&([i,l]=t.tag(),!s||l!=O.EndGroup);){const d=e.findNumber(i);if(d)xa(e,t,d,l,n);else{const m=t.skip(l,i);n.readUnknownFields&&u.push({no:i,wireType:l,data:m})}}if(s&&(l!=O.EndGroup||i!==r))throw new Error("invalid end group tag");u.length>0&&e.setUnknown(u)}function xa(e,t,n,s,r){switch(n.fieldKind){case"scalar":e.set(n,Ye(t,n.scalar));break;case"enum":e.set(n,Ye(t,g.INT32));break;case"message":e.set(n,_n(t,r,n,e.get(n)));break;case"list":(function(a,o,i,l){var u;const d=i.field();if(d.listKind==="message")return void i.add(_n(a,l,d));const m=(u=d.scalar)!==null&&u!==void 0?u:g.INT32;if(!(o==O.LengthDelimited&&m!=g.STRING&&m!=g.BYTES))return void i.add(Ye(a,m));const p=a.uint32()+a.pos;for(;a.pos<p;)i.add(Ye(a,m))})(t,s,e.get(n),r);break;case"map":(function(a,o,i){const l=o.field();let u,d;const m=a.pos+a.uint32();for(;a.pos<m;){const[h]=a.tag();switch(h){case 1:u=Ye(a,l.mapKey);break;case 2:switch(l.mapKind){case"scalar":d=Ye(a,l.scalar);break;case"enum":d=a.int32();break;case"message":d=_n(a,i,l)}}}if(u===void 0&&(u=Le(l.mapKey,!1)),d===void 0)switch(l.mapKind){case"scalar":d=Le(l.scalar,!1);break;case"enum":d=l.enum.values[0].number;break;case"message":d=ie(l.message,void 0,!1)}o.set(u,d)})(t,e.get(n),r)}}function _n(e,t,n,s){const r=n.delimitedEncoding,a=s??ie(n.message,void 0,!1);return wa(a,e,t,r,r?n.number:e.uint32()),a}function Ye(e,t){switch(t){case g.STRING:return e.string();case g.BOOL:return e.bool();case g.DOUBLE:return e.double();case g.FLOAT:return e.float();case g.INT32:return e.int32();case g.INT64:return e.int64();case g.UINT64:return e.uint64();case g.FIXED64:return e.fixed64();case g.BYTES:return e.bytes();case g.FIXED32:return e.fixed32();case g.SFIXED32:return e.sfixed32();case g.SFIXED64:return e.sfixed64();case g.SINT64:return e.sint64();case g.UINT32:return e.uint32();case g.SINT32:return e.sint32()}}function cs(e,t){const n=us(bi,_a(e));return n.messageType.forEach(ls),n.dependency=[],va(n,s=>{}).getFile(n.name)}const Ei=_t(cs("Chlnb29nbGUvcHJvdG9idWYvYW55LnByb3RvEg9nb29nbGUucHJvdG9idWYiJgoDQW55EhAKCHR5cGVfdXJsGAEgASgJEg0KBXZhbHVlGAIgASgMQnYKE2NvbS5nb29nbGUucHJvdG9idWZCCEFueVByb3RvUAFaLGdvb2dsZS5nb2xhbmcub3JnL3Byb3RvYnVmL3R5cGVzL2tub3duL2FueXBiogIDR1BCqgIeR29vZ2xlLlByb3RvYnVmLldlbGxLbm93blR5cGVzYgZwcm90bzM"),0),Ti=3,rr={writeUnknownFields:!0};function Si(e,t,n){return Ht(new la,function(s){return s?Object.assign(Object.assign({},rr),s):rr}(n),ie(e,t)).finish()}function Ht(e,t,n){var s;for(const r of n.sortedFields)if(n.isSet(r))Ca(e,t,n,r);else if(r.presence==Ti)throw new Error(`cannot encode ${r} to binary: required field not set`);if(t.writeUnknownFields)for(const{no:r,wireType:a,data:o}of(s=n.getUnknown())!==null&&s!==void 0?s:[])e.tag(r,a).raw(o);return e}function Ca(e,t,n,s){var r;switch(s.fieldKind){case"scalar":case"enum":Gt(e,n.desc.typeName,s.name,(r=s.scalar)!==null&&r!==void 0?r:g.INT32,s.number,n.get(s));break;case"list":(function(a,o,i,l){var u;if(i.listKind=="message"){for(const m of l)ar(a,o,i,m);return}const d=(u=i.scalar)!==null&&u!==void 0?u:g.INT32;if(i.packed){if(!l.size)return;a.tag(i.number,O.LengthDelimited).fork();for(const m of l)ka(a,i.parent.typeName,i.name,d,m);return void a.join()}for(const m of l)Gt(a,i.parent.typeName,i.name,d,i.number,m)})(e,t,s,n.get(s));break;case"message":ar(e,t,s,n.get(s));break;case"map":for(const[a,o]of n.get(s))Ii(e,t,s,a,o)}}function Gt(e,t,n,s,r,a){ka(e.tag(r,function(o){switch(o){case g.BYTES:case g.STRING:return O.LengthDelimited;case g.DOUBLE:case g.FIXED64:case g.SFIXED64:return O.Bit64;case g.FIXED32:case g.SFIXED32:case g.FLOAT:return O.Bit32;default:return O.Varint}}(s)),t,n,s,a)}function ar(e,t,n,s){n.delimitedEncoding?Ht(e.tag(n.number,O.StartGroup),t,s).tag(n.number,O.EndGroup):Ht(e.tag(n.number,O.LengthDelimited).fork(),t,s).join()}function Ii(e,t,n,s,r){var a;switch(e.tag(n.number,O.LengthDelimited).fork(),Gt(e,n.parent.typeName,n.name,n.mapKey,1,s),n.mapKind){case"scalar":case"enum":Gt(e,n.parent.typeName,n.name,(a=n.scalar)!==null&&a!==void 0?a:g.INT32,2,r);break;case"message":Ht(e.tag(2,O.LengthDelimited).fork(),t,r).join()}e.join()}function ka(e,t,n,s,r){try{switch(s){case g.STRING:e.string(r);break;case g.BOOL:e.bool(r);break;case g.DOUBLE:e.double(r);break;case g.FLOAT:e.float(r);break;case g.INT32:e.int32(r);break;case g.INT64:e.int64(r);break;case g.UINT64:e.uint64(r);break;case g.FIXED64:e.fixed64(r);break;case g.BYTES:e.bytes(r);break;case g.FIXED32:e.fixed32(r);break;case g.SFIXED32:e.sfixed32(r);break;case g.SFIXED64:e.sfixed64(r);break;case g.SINT64:e.sint64(r);break;case g.UINT32:e.uint32(r);break;case g.SINT32:e.sint32(r)}}catch(a){throw a instanceof Error?new Error(`cannot encode field ${t}.${n} to binary: ${a.message}`):a}}function Ni(e,t){if(e.typeUrl==="")return;const n=t.kind=="message"?t:t.getMessage(or(e.typeUrl));return n&&function(s,r){return s.typeUrl!==""&&(typeof r=="string"?r:r.typeName)===or(s.typeUrl)}(e,n)?us(n,e.value):void 0}function or(e){const t=e.lastIndexOf("/"),n=t>=0?e.substring(t+1):e;if(!n.length)throw new Error(`invalid type url: ${e}`);return n}const ds=cs("Chxnb29nbGUvcHJvdG9idWYvc3RydWN0LnByb3RvEg9nb29nbGUucHJvdG9idWYihAEKBlN0cnVjdBIzCgZmaWVsZHMYASADKAsyIy5nb29nbGUucHJvdG9idWYuU3RydWN0LkZpZWxkc0VudHJ5GkUKC0ZpZWxkc0VudHJ5EgsKA2tleRgBIAEoCRIlCgV2YWx1ZRgCIAEoCzIWLmdvb2dsZS5wcm90b2J1Zi5WYWx1ZToCOAEi6gEKBVZhbHVlEjAKCm51bGxfdmFsdWUYASABKA4yGi5nb29nbGUucHJvdG9idWYuTnVsbFZhbHVlSAASFgoMbnVtYmVyX3ZhbHVlGAIgASgBSAASFgoMc3RyaW5nX3ZhbHVlGAMgASgJSAASFAoKYm9vbF92YWx1ZRgEIAEoCEgAEi8KDHN0cnVjdF92YWx1ZRgFIAEoCzIXLmdvb2dsZS5wcm90b2J1Zi5TdHJ1Y3RIABIwCgpsaXN0X3ZhbHVlGAYgASgLMhouZ29vZ2xlLnByb3RvYnVmLkxpc3RWYWx1ZUgAQgYKBGtpbmQiMwoJTGlzdFZhbHVlEiYKBnZhbHVlcxgBIAMoCzIWLmdvb2dsZS5wcm90b2J1Zi5WYWx1ZSobCglOdWxsVmFsdWUSDgoKTlVMTF9WQUxVRRAAQn8KE2NvbS5nb29nbGUucHJvdG9idWZCC1N0cnVjdFByb3RvUAFaL2dvb2dsZS5nb2xhbmcub3JnL3Byb3RvYnVmL3R5cGVzL2tub3duL3N0cnVjdHBi+AEBogIDR1BCqgIeR29vZ2xlLlByb3RvYnVmLldlbGxLbm93blR5cGVzYgZwcm90bzM"),wi=_t(ds,0),Ra=_t(ds,1),xi=_t(ds,2);var qn;function Ci(e,t){Aa(t,e);const n=function(o,i){if(o===void 0)return[];if(i.fieldKind==="enum"||i.fieldKind==="scalar"){for(let l=o.length-1;l>=0;--l)if(o[l].no==i.number)return[o[l]];return[]}return o.filter(l=>l.no===i.number)}(e.$unknown,t),[s,r,a]=rn(t);for(const o of n)xa(s,new ns(o.data),r,o.wireType,{readUnknownFields:!0});return a()}function ki(e,t,n){var s;Aa(t,e);const r=((s=e.$unknown)!==null&&s!==void 0?s:[]).filter(u=>u.no!==t.number),[a,o]=rn(t,n),i=new la;Ca(i,{writeUnknownFields:!0},a,o);const l=new ns(i.finish());for(;l.pos<l.len;){const[u,d]=l.tag(),m=l.skip(d,u);r.push({no:u,wireType:d,data:m})}e.$unknown=r}function rn(e,t){const n=e.typeName,s=Object.assign(Object.assign({},e),{kind:"field",parent:e.extendee,localName:n}),r=Object.assign(Object.assign({},e.extendee),{fields:[s],members:[s],oneofs:[]}),a=_e(r,t!==void 0?{[n]:t}:void 0);return[ie(r,a),s,()=>{const o=a[n];if(o===void 0){const i=e.message;return yt(i)?Le(i.fields[0].scalar,i.fields[0].longAsString):_e(i)}return o}]}function Aa(e,t){if(e.extendee.typeName!=t.$typeName)throw new Error(`extension ${e.typeName} can only be applied to message ${e.extendee.typeName}`)}(function(e){e[e.NULL_VALUE=0]="NULL_VALUE"})(qn||(qn={}));const Ri=3,Ai=2,ir={alwaysEmitImplicit:!1,enumAsInteger:!1,useProtoFieldName:!1};function Oi(e,t,n){return it(ie(e,t),function(s){return s?Object.assign(Object.assign({},ir),s):ir}(n))}function it(e,t){var n;const s=function(a,o){if(a.desc.typeName.startsWith("google.protobuf.")){switch(a.desc.typeName){case"google.protobuf.Any":return function(l,u){if(l.typeUrl==="")return{};const{registry:d}=u;let m,h;if(d&&(m=Ni(l,d),m&&(h=d.getMessage(m.$typeName))),!h||!m)throw new Error(`cannot encode message ${l.$typeName} to JSON: "${l.typeUrl}" is not in the type registry`);let p=it(ie(h,m),u);return(h.typeName.startsWith("google.protobuf.")||p===null||Array.isArray(p)||typeof p!="object")&&(p={value:p}),p["@type"]=l.typeUrl,p}(a.message,o);case"google.protobuf.Timestamp":return function(l){const u=1e3*Number(l.seconds);if(u<Date.parse("0001-01-01T00:00:00Z")||u>Date.parse("9999-12-31T23:59:59Z"))throw new Error(`cannot encode message ${l.$typeName} to JSON: must be from 0001-01-01T00:00:00Z to 9999-12-31T23:59:59Z inclusive`);if(l.nanos<0)throw new Error(`cannot encode message ${l.$typeName} to JSON: nanos must not be negative`);let d="Z";if(l.nanos>0){const m=(l.nanos+1e9).toString().substring(1);d=m.substring(3)==="000000"?"."+m.substring(0,3)+"Z":m.substring(6)==="000"?"."+m.substring(0,6)+"Z":"."+m+"Z"}return new Date(u).toISOString().replace(".000Z",d)}(a.message);case"google.protobuf.Duration":return function(l){if(Number(l.seconds)>315576e6||Number(l.seconds)<-315576e6)throw new Error(`cannot encode message ${l.$typeName} to JSON: value out of range`);let u=l.seconds.toString();if(l.nanos!==0){let d=Math.abs(l.nanos).toString();d="0".repeat(9-d.length)+d,d.substring(3)==="000000"?d=d.substring(0,3):d.substring(6)==="000"&&(d=d.substring(0,6)),u+="."+d,l.nanos<0&&Number(l.seconds)==0&&(u="-"+u)}return u+"s"}(a.message);case"google.protobuf.FieldMask":return(i=a.message).paths.map(l=>{if(l.match(/_[0-9]?_/g)||l.match(/[A-Z]/g))throw new Error(`cannot encode message ${i.$typeName} to JSON: lowerCamelCase of path name "`+l+'" is irreversible');return ht(l)}).join(",");case"google.protobuf.Struct":return Oa(a.message);case"google.protobuf.Value":return ms(a.message);case"google.protobuf.ListValue":return Ma(a.message);default:if(yt(a.desc)){const l=a.desc.fields[0];return Mt(l,a.get(l))}return}var i}}(e,t);if(s!==void 0)return s;const r={};for(const a of e.sortedFields){if(!e.isSet(a)){if(a.presence==Ri)throw new Error(`cannot encode ${a} to JSON: required field not set`);if(!t.alwaysEmitImplicit||a.presence!==Ai)continue}const o=lr(a,e.get(a),t);o!==void 0&&(r[Mi(a,t)]=o)}if(t.registry){const a=new Set;for(const{no:o}of(n=e.getUnknown())!==null&&n!==void 0?n:[])if(!a.has(o)){a.add(o);const i=t.registry.getExtensionFor(e.desc,o);if(!i)continue;const l=Ci(e.message,i),[u,d]=rn(i,l),m=lr(d,u.get(d),t);m!==void 0&&(r[i.jsonName]=m)}}return r}function lr(e,t,n){switch(e.fieldKind){case"scalar":return Mt(e,t);case"message":return it(t,n);case"enum":return bn(e.enum,t,n.enumAsInteger);case"list":return function(s,r){const a=s.field(),o=[];switch(a.listKind){case"scalar":for(const i of s)o.push(Mt(a,i));break;case"enum":for(const i of s)o.push(bn(a.enum,i,r.enumAsInteger));break;case"message":for(const i of s)o.push(it(i,r))}return r.alwaysEmitImplicit||o.length>0?o:void 0}(t,n);case"map":return function(s,r){const a=s.field(),o={};switch(a.mapKind){case"scalar":for(const[i,l]of s)o[i]=Mt(a,l);break;case"message":for(const[i,l]of s)o[i]=it(l,r);break;case"enum":for(const[i,l]of s)o[i]=bn(a.enum,l,r.enumAsInteger)}return r.alwaysEmitImplicit||s.size>0?o:void 0}(t,n)}}function bn(e,t,n){var s;if(typeof t!="number")throw new Error(`cannot encode ${e} to JSON: expected number, got ${F(t)}`);if(e.typeName=="google.protobuf.NullValue")return null;if(n)return t;const r=e.value[t];return(s=r==null?void 0:r.name)!==null&&s!==void 0?s:t}function Mt(e,t){var n,s,r,a,o,i;switch(e.scalar){case g.INT32:case g.SFIXED32:case g.SINT32:case g.FIXED32:case g.UINT32:if(typeof t!="number")throw new Error(`cannot encode ${e} to JSON: ${(n=De(e,t))===null||n===void 0?void 0:n.message}`);return t;case g.FLOAT:case g.DOUBLE:if(typeof t!="number")throw new Error(`cannot encode ${e} to JSON: ${(s=De(e,t))===null||s===void 0?void 0:s.message}`);return Number.isNaN(t)?"NaN":t===Number.POSITIVE_INFINITY?"Infinity":t===Number.NEGATIVE_INFINITY?"-Infinity":t;case g.STRING:if(typeof t!="string")throw new Error(`cannot encode ${e} to JSON: ${(r=De(e,t))===null||r===void 0?void 0:r.message}`);return t;case g.BOOL:if(typeof t!="boolean")throw new Error(`cannot encode ${e} to JSON: ${(a=De(e,t))===null||a===void 0?void 0:a.message}`);return t;case g.UINT64:case g.FIXED64:case g.INT64:case g.SFIXED64:case g.SINT64:if(typeof t!="bigint"&&typeof t!="string")throw new Error(`cannot encode ${e} to JSON: ${(o=De(e,t))===null||o===void 0?void 0:o.message}`);return t.toString();case g.BYTES:if(t instanceof Uint8Array)return function(l,u="std"){const d=ba(u),m=u=="std";let h,p="",f=0,b=0;for(let _=0;_<l.length;_++)switch(h=l[_],f){case 0:p+=d[h>>2],b=(3&h)<<4,f=1;break;case 1:p+=d[b|h>>4],b=(15&h)<<2,f=2;break;case 2:p+=d[b|h>>6],p+=d[63&h],f=0}return f&&(p+=d[b],m&&(p+="=",f==1&&(p+="="))),p}(t);throw new Error(`cannot encode ${e} to JSON: ${(i=De(e,t))===null||i===void 0?void 0:i.message}`)}}function Mi(e,t){return t.useProtoFieldName?e.name:e.jsonName}function Oa(e){const t={};for(const[n,s]of Object.entries(e.fields))t[n]=ms(s);return t}function ms(e){switch(e.kind.case){case"nullValue":return null;case"numberValue":if(!Number.isFinite(e.kind.value))throw new Error(`${e.$typeName} cannot be NaN or Infinity`);return e.kind.value;case"boolValue":case"stringValue":return e.kind.value;case"structValue":return Oa(e.kind.value);case"listValue":return Ma(e.kind.value);default:throw new Error(`${e.$typeName} must have a value`)}}function Ma(e){return e.values.map(ms)}const ur={ignoreUnknownFields:!1};function Di(e,t,n){const s=ie(e);try{ze(s,t,function(a){return a?Object.assign(Object.assign({},ur),a):ur}(n))}catch(a){throw(r=a)instanceof Error&&Ko.includes(r.name)&&"field"in r&&typeof r.field=="function"?new Error(`cannot decode ${a.field()} from JSON: ${a.message}`,{cause:a}):a}var r;return s.message}function ze(e,t,n){var s;if(function(o,i,l){if(!o.desc.typeName.startsWith("google.protobuf."))return!1;switch(o.desc.typeName){case"google.protobuf.Any":return function(u,d,m){var h;if(d===null||Array.isArray(d)||typeof d!="object")throw new Error(`cannot decode message ${u.$typeName} from JSON: expected object but got ${F(d)}`);if(Object.keys(d).length==0)return;const p=d["@type"];if(typeof p!="string"||p=="")throw new Error(`cannot decode message ${u.$typeName} from JSON: "@type" is empty`);const f=p.includes("/")?p.substring(p.lastIndexOf("/")+1):p;if(!f.length)throw new Error(`cannot decode message ${u.$typeName} from JSON: "@type" is invalid`);const b=(h=m.registry)===null||h===void 0?void 0:h.getMessage(f);if(!b)throw new Error(`cannot decode message ${u.$typeName} from JSON: ${p} is not in the type registry`);const _=ie(b);if(f.startsWith("google.protobuf.")&&Object.prototype.hasOwnProperty.call(d,"value"))ze(_,d.value,m);else{const E=Object.assign({},d);delete E["@type"],ze(_,E,m)}(function(E,C,y){let oe=!1;y||(y=_e(Ei),oe=!0),y.value=Si(E,C),y.typeUrl=`type.googleapis.com/${C.$typeName}`})(_.desc,_.message,u)}(o.message,i,l),!0;case"google.protobuf.Timestamp":return function(u,d){if(typeof d!="string")throw new Error(`cannot decode message ${u.$typeName} from JSON: ${F(d)}`);const m=d.match(/^([0-9]{4})-([0-9]{2})-([0-9]{2})T([0-9]{2}):([0-9]{2}):([0-9]{2})(?:\.([0-9]{1,9}))?(?:Z|([+-][0-9][0-9]:[0-9][0-9]))$/);if(!m)throw new Error(`cannot decode message ${u.$typeName} from JSON: invalid RFC 3339 string`);const h=Date.parse(m[1]+"-"+m[2]+"-"+m[3]+"T"+m[4]+":"+m[5]+":"+m[6]+(m[8]?m[8]:"Z"));if(Number.isNaN(h))throw new Error(`cannot decode message ${u.$typeName} from JSON: invalid RFC 3339 string`);if(h<Date.parse("0001-01-01T00:00:00Z")||h>Date.parse("9999-12-31T23:59:59Z"))throw new Error(`cannot decode message ${u.$typeName} from JSON: must be from 0001-01-01T00:00:00Z to 9999-12-31T23:59:59Z inclusive`);u.seconds=k.parse(h/1e3),u.nanos=0,m[7]&&(u.nanos=parseInt("1"+m[7]+"0".repeat(9-m[7].length))-1e9)}(o.message,i),!0;case"google.protobuf.Duration":return function(u,d){if(typeof d!="string")throw new Error(`cannot decode message ${u.$typeName} from JSON: ${F(d)}`);const m=d.match(/^(-?[0-9]+)(?:\.([0-9]+))?s/);if(m===null)throw new Error(`cannot decode message ${u.$typeName} from JSON: ${F(d)}`);const h=Number(m[1]);if(h>315576e6||h<-315576e6)throw new Error(`cannot decode message ${u.$typeName} from JSON: ${F(d)}`);if(u.seconds=k.parse(h),typeof m[2]!="string")return;const p=m[2]+"0".repeat(9-m[2].length);u.nanos=parseInt(p),(h<0||Object.is(h,-0))&&(u.nanos=-u.nanos)}(o.message,i),!0;case"google.protobuf.FieldMask":return function(u,d){if(typeof d!="string")throw new Error(`cannot decode message ${u.$typeName} from JSON: ${F(d)}`);if(d==="")return;function m(h){if(h.includes("_"))throw new Error(`cannot decode message ${u.$typeName} from JSON: path names must be lowerCamelCase`);const p=h.replace(/[A-Z]/g,f=>"_"+f.toLowerCase());return p[0]==="_"?p.substring(1):p}u.paths=d.split(",").map(m)}(o.message,i),!0;case"google.protobuf.Struct":return $a(o.message,i),!0;case"google.protobuf.Value":return hs(o.message,i),!0;case"google.protobuf.ListValue":return Fa(o.message,i),!0;default:if(yt(o.desc)){const u=o.desc.fields[0];return i===null?o.clear(u):o.set(u,$t(u,i,!0)),!0}return!1}}(e,t,n))return;if(t==null||Array.isArray(t)||typeof t!="object")throw new Error(`cannot decode ${e.desc} from JSON: ${F(t)}`);const r=new Map,a=new Map;for(const o of e.desc.fields)a.set(o.name,o).set(o.jsonName,o);for(const[o,i]of Object.entries(t)){const l=a.get(o);if(l){if(l.oneof){if(i===null&&l.fieldKind=="scalar")continue;const u=r.get(l.oneof);if(u!==void 0)throw new z(l.oneof,`oneof set multiple times by ${u.name} and ${l.name}`);r.set(l.oneof,l)}cr(e,l,i,n)}else{let u;if(o.startsWith("[")&&o.endsWith("]")&&(u=(s=n.registry)===null||s===void 0?void 0:s.getExtension(o.substring(1,o.length-1)))&&u.extendee.typeName===e.desc.typeName){const[d,m,h]=rn(u);cr(d,m,i,n),ki(e.message,u,h())}if(!u&&!n.ignoreUnknownFields)throw new Error(`cannot decode ${e.desc} from JSON: key "${o}" is unknown`)}}}function cr(e,t,n,s){switch(t.fieldKind){case"scalar":(function(r,a,o){const i=$t(a,o,!1);i===Vt?r.clear(a):r.set(a,i)})(e,t,n);break;case"enum":(function(r,a,o,i){const l=vn(a.enum,o,i.ignoreUnknownFields,!1);l===Vt?r.clear(a):l!==Dt&&r.set(a,l)})(e,t,n,s);break;case"message":(function(r,a,o,i){if(o===null&&a.message.typeName!="google.protobuf.Value")return void r.clear(a);const l=r.isSet(a)?r.get(a):ie(a.message);ze(l,o,i),r.set(a,l)})(e,t,n,s);break;case"list":(function(r,a,o){if(a===null)return;const i=r.field();if(!Array.isArray(a))throw new z(i,"expected Array, got "+F(a));for(const l of a){if(l===null)throw new z(i,"list item must not be null");switch(i.listKind){case"message":const u=ie(i.message);ze(u,l,o),r.add(u);break;case"enum":const d=vn(i.enum,l,o.ignoreUnknownFields,!0);d!==Dt&&r.add(d);break;case"scalar":r.add($t(i,l,!0))}}})(e.get(t),n,s);break;case"map":(function(r,a,o){if(a===null)return;const i=r.field();if(typeof a!="object"||Array.isArray(a))throw new z(i,"expected object, got "+F(a));for(const[l,u]of Object.entries(a)){if(u===null)throw new z(i,"map value must not be null");let d;switch(i.mapKind){case"message":const h=ie(i.message);ze(h,u,o),d=h;break;case"enum":if(d=vn(i.enum,u,o.ignoreUnknownFields,!0),d===Dt)return;break;case"scalar":d=$t(i,u,!0)}const m=$i(i.mapKey,l);r.set(m,d)}})(e.get(t),n,s)}}const Dt=Symbol();function vn(e,t,n,s){if(t===null)return e.typeName=="google.protobuf.NullValue"?0:s?e.values[0].number:Vt;switch(typeof t){case"number":if(Number.isInteger(t))return t;break;case"string":const r=e.values.find(a=>a.name===t);if(r!==void 0)return r.number;if(n)return Dt}throw new Error(`cannot decode ${e} from JSON: ${F(t)}`)}const Vt=Symbol();function $t(e,t,n){if(t===null)return n?Le(e.scalar,!1):Vt;switch(e.scalar){case g.DOUBLE:case g.FLOAT:if(t==="NaN")return NaN;if(t==="Infinity")return Number.POSITIVE_INFINITY;if(t==="-Infinity")return Number.NEGATIVE_INFINITY;if(typeof t=="number"){if(Number.isNaN(t))throw new z(e,"unexpected NaN number");if(!Number.isFinite(t))throw new z(e,"unexpected infinite number");break}if(typeof t=="string"){if(t===""||t.trim().length!==t.length)break;const s=Number(t);if(!Number.isFinite(s))break;return s}break;case g.INT32:case g.FIXED32:case g.SFIXED32:case g.SINT32:case g.UINT32:return Da(t);case g.BYTES:if(typeof t=="string"){if(t==="")return new Uint8Array(0);try{return _a(t)}catch(s){const r=s instanceof Error?s.message:String(s);throw new z(e,r)}}}return t}function $i(e,t){switch(e){case g.BOOL:switch(t){case"true":return!0;case"false":return!1}return t;case g.INT32:case g.FIXED32:case g.UINT32:case g.SFIXED32:case g.SINT32:return Da(t);default:return t}}function Da(e){if(typeof e=="string"){if(e===""||e.trim().length!==e.length)return e;const t=Number(e);return Number.isNaN(t)?e:t}return e}function $a(e,t){if(typeof t!="object"||t==null||Array.isArray(t))throw new Error(`cannot decode message ${e.$typeName} from JSON ${F(t)}`);for(const[n,s]of Object.entries(t)){const r=_e(Ra);hs(r,s),e.fields[n]=r}}function hs(e,t){switch(typeof t){case"number":e.kind={case:"numberValue",value:t};break;case"string":e.kind={case:"stringValue",value:t};break;case"boolean":e.kind={case:"boolValue",value:t};break;case"object":if(t===null)e.kind={case:"nullValue",value:qn.NULL_VALUE};else if(Array.isArray(t)){const n=_e(xi);Fa(n,t),e.kind={case:"listValue",value:n}}else{const n=_e(wi);$a(n,t),e.kind={case:"structValue",value:n}}break;default:throw new Error(`cannot decode message ${e.$typeName} from JSON ${F(t)}`)}return e}function Fa(e,t){if(!Array.isArray(t))throw new Error(`cannot decode message ${e.$typeName} from JSON ${F(t)}`);for(const n of t){const s=_e(Ra);hs(s,n),e.values.push(s)}}class Ua{constructor(t){c(this,"target");c(this,"pendingRequests",new Map);c(this,"cleanup");c(this,"serviceRegistries",new Set);this.target=t,this.cleanup=this.target.onReceiveMessage(this.handleMessage.bind(this))}addServiceRegistry(t){this.serviceRegistries.add(t)}removeServiceRegistry(t){this.serviceRegistries.delete(t)}handleMessage(t){if(!t||typeof t!="object"||!this.isGrpcMessageLike(t))return;const n=t;n.type==="com.augmentcode.client.rpc.request"?this.handleRequest(n):n.type==="com.augmentcode.client.rpc.response"&&this.handleResponse(n)}isGrpcMessageLike(t){return"type"in t&&t.type==="com.augmentcode.client.rpc.request"||t.type==="com.augmentcode.client.rpc.response"}async handleRequest(t){for(const n of this.serviceRegistries)if(n.canHandle(t))try{return void await n.handleRequest(t,s=>{this.target.sendMessage(s)})}catch(s){Array.from(this.serviceRegistries).indexOf(n)===this.serviceRegistries.size-1&&this.target.sendMessage({type:"com.augmentcode.client.rpc.response",id:t.id,methodLocalName:t.methodLocalName,serviceTypeName:t.serviceTypeName,data:"",error:s instanceof Error?s.message:String(s)})}this.target.sendMessage({type:"com.augmentcode.client.rpc.response",id:t.id,methodLocalName:t.methodLocalName,serviceTypeName:t.serviceTypeName,data:"",error:`No handlers registered for service: ${t.serviceTypeName}`})}handleResponse(t){const n=this.pendingRequests.get(t.id);if(n)if(this.pendingRequests.delete(t.id),clearTimeout(n.timeout),t.error)n.reject(new Error(`gRPC server error for ${t.serviceTypeName}.${t.methodLocalName} (ID: ${t.id}): ${t.error}`));else try{if(!t.data&&t.data!==null&&t.data!=="")throw new Error(`gRPC response missing data field for ${t.serviceTypeName}.${t.methodLocalName} (ID: ${t.id})`);n.resolve(t)}catch(s){const r=s instanceof Error?s.message:String(s);n.reject(new Error(`Failed to process gRPC response for ${t.serviceTypeName}.${t.methodLocalName} (ID: ${t.id}): ${r}`))}}sendRequest(t,n){return new Promise((s,r)=>{let a;n&&(a=setTimeout(()=>{this.pendingRequests.delete(t.id),r(new Error(`gRPC request timed out after ${n}ms: ${t.serviceTypeName}.${t.methodLocalName} (ID: ${t.id}). This may indicate that the server is not responding or the message routing is broken.`))},n)),this.pendingRequests.set(t.id,{resolve:s,reject:r,timeout:a}),this.target.sendMessage(t)})}async unary(t,n,s,r,a,o){const i=crypto.randomUUID(),l=t.localName,u=t.parent.typeName;if(!u)throw new Error("Service name is required for unary calls");const d=a?Oi(t.input,_e(t.input,a)):{};if(n!=null&&n.aborted)throw new Error(`gRPC request aborted before sending: ${u}.${l} (ID: ${i})`);let m;n&&(m=()=>{const p=this.pendingRequests.get(i);p&&(this.pendingRequests.delete(i),clearTimeout(p.timeout),p.reject(new Error(`gRPC request aborted during execution: ${u}.${l} (ID: ${i})`)))},n.addEventListener("abort",m));const h=await this.sendRequest({type:"com.augmentcode.client.rpc.request",id:i,methodLocalName:l,serviceTypeName:u,data:d,timeout:s},s);return n&&m&&n.removeEventListener("abort",m),{stream:!1,method:t,service:t.parent,header:new Headers(r),message:Di(t.output,h.data),trailer:new Headers}}stream(t,n,s,r,a,o){throw new Error("Streaming is not supported by this transport")}dispose(){this.cleanup();for(const{timeout:t}of this.pendingRequests.values())clearTimeout(t);this.pendingRequests.clear(),this.serviceRegistries.clear()}}c(Ua,"PROTOCOL_NAME","com.augmentcode.client.rpc");var Re;function dr(e){const t=Re[e];return typeof t!="string"?e.toString():t[0].toLowerCase()+t.substring(1).replace(/[A-Z]/g,n=>"_"+n.toLowerCase())}(function(e){e[e.Canceled=1]="Canceled",e[e.Unknown=2]="Unknown",e[e.InvalidArgument=3]="InvalidArgument",e[e.DeadlineExceeded=4]="DeadlineExceeded",e[e.NotFound=5]="NotFound",e[e.AlreadyExists=6]="AlreadyExists",e[e.PermissionDenied=7]="PermissionDenied",e[e.ResourceExhausted=8]="ResourceExhausted",e[e.FailedPrecondition=9]="FailedPrecondition",e[e.Aborted=10]="Aborted",e[e.OutOfRange=11]="OutOfRange",e[e.Unimplemented=12]="Unimplemented",e[e.Internal=13]="Internal",e[e.Unavailable=14]="Unavailable",e[e.DataLoss=15]="DataLoss",e[e.Unauthenticated=16]="Unauthenticated"})(Re||(Re={}));class we extends Error{constructor(t,n=Re.Unknown,s,r,a){super(function(o,i){return o.length?`[${dr(i)}] ${o}`:`[${dr(i)}]`}(t,n)),this.name="ConnectError",Object.setPrototypeOf(this,new.target.prototype),this.rawMessage=t,this.code=n,this.metadata=new Headers(s??{}),this.details=r??[],this.cause=a}static from(t,n=Re.Unknown){return t instanceof we?t:t instanceof Error?t.name=="AbortError"?new we(t.message,Re.Canceled):new we(t.message,n,void 0,void 0,t):new we(String(t),n,void 0,void 0,t)}static[Symbol.hasInstance](t){return t instanceof Error&&(Object.getPrototypeOf(t)===we.prototype||t.name==="ConnectError"&&"code"in t&&typeof t.code=="number"&&"metadata"in t&&"details"in t&&Array.isArray(t.details)&&"rawMessage"in t&&typeof t.rawMessage=="string"&&"cause"in t)}findDetails(t){const n=t.kind==="message"?{getMessage:r=>r===t.typeName?t:void 0}:t,s=[];for(const r of this.details){if("desc"in r){n.getMessage(r.desc.typeName)&&s.push(_e(r.desc,r.value));continue}const a=n.getMessage(r.type);if(a)try{s.push(us(a,r.value))}catch{}}return s}}var Fi=function(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t,n=e[Symbol.asyncIterator];return n?n.call(e):(e=typeof __values=="function"?__values(e):e[Symbol.iterator](),t={},s("next"),s("throw"),s("return"),t[Symbol.asyncIterator]=function(){return this},t);function s(r){t[r]=e[r]&&function(a){return new Promise(function(o,i){(function(l,u,d,m){Promise.resolve(m).then(function(h){l({value:h,done:d})},u)})(o,i,(a=e[r](a)).done,a.value)})}}},gt=function(e){return this instanceof gt?(this.v=e,this):new gt(e)},Ui=function(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var s,r=n.apply(e,t||[]),a=[];return s=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),o("next"),o("throw"),o("return",function(m){return function(h){return Promise.resolve(h).then(m,u)}}),s[Symbol.asyncIterator]=function(){return this},s;function o(m,h){r[m]&&(s[m]=function(p){return new Promise(function(f,b){a.push([m,p,f,b])>1||i(m,p)})},h&&(s[m]=h(s[m])))}function i(m,h){try{(p=r[m](h)).value instanceof gt?Promise.resolve(p.value.v).then(l,u):d(a[0][2],p)}catch(f){d(a[0][3],f)}var p}function l(m){i("next",m)}function u(m){i("throw",m)}function d(m,h){m(h),a.shift(),a.length&&i(a[0][0],a[0][1])}},Pi=function(e){var t,n;return t={},s("next"),s("throw",function(r){throw r}),s("return"),t[Symbol.iterator]=function(){return this},t;function s(r,a){t[r]=e[r]?function(o){return(n=!n)?{value:gt(e[r](o)),done:!1}:a?a(o):o}:a}},Pa=function(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t,n=e[Symbol.asyncIterator];return n?n.call(e):(e=typeof __values=="function"?__values(e):e[Symbol.iterator](),t={},s("next"),s("throw"),s("return"),t[Symbol.asyncIterator]=function(){return this},t);function s(r){t[r]=e[r]&&function(a){return new Promise(function(o,i){(function(l,u,d,m){Promise.resolve(m).then(function(h){l({value:h,done:d})},u)})(o,i,(a=e[r](a)).done,a.value)})}}},Je=function(e){return this instanceof Je?(this.v=e,this):new Je(e)},Li=function(e){var t,n;return t={},s("next"),s("throw",function(r){throw r}),s("return"),t[Symbol.iterator]=function(){return this},t;function s(r,a){t[r]=e[r]?function(o){return(n=!n)?{value:Je(e[r](o)),done:!1}:a?a(o):o}:a}},qi=function(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var s,r=n.apply(e,t||[]),a=[];return s=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),o("next"),o("throw"),o("return",function(m){return function(h){return Promise.resolve(h).then(m,u)}}),s[Symbol.asyncIterator]=function(){return this},s;function o(m,h){r[m]&&(s[m]=function(p){return new Promise(function(f,b){a.push([m,p,f,b])>1||i(m,p)})},h&&(s[m]=h(s[m])))}function i(m,h){try{(p=r[m](h)).value instanceof Je?Promise.resolve(p.value.v).then(l,u):d(a[0][2],p)}catch(f){d(a[0][3],f)}var p}function l(m){i("next",m)}function u(m){i("throw",m)}function d(m,h){m(h),a.shift(),a.length&&i(a[0][0],a[0][1])}};function Hi(e,t){return function(n,s){const r={};for(const a of n.methods){const o=s(a);o!=null&&(r[a.localName]=o)}return r}(e,n=>{switch(n.methodKind){case"unary":return function(s,r){return async function(a,o){var i,l;const u=await s.unary(r,o==null?void 0:o.signal,o==null?void 0:o.timeoutMs,o==null?void 0:o.headers,a,o==null?void 0:o.contextValues);return(i=o==null?void 0:o.onHeader)===null||i===void 0||i.call(o,u.header),(l=o==null?void 0:o.onTrailer)===null||l===void 0||l.call(o,u.trailer),u.message}}(t,n);case"server_streaming":return function(s,r){return function(a,o){return mr(s.stream(r,o==null?void 0:o.signal,o==null?void 0:o.timeoutMs,o==null?void 0:o.headers,function(i){return Ui(this,arguments,function*(){yield gt(yield*Pi(Fi(i)))})}([a]),o==null?void 0:o.contextValues),o)}}(t,n);case"client_streaming":return function(s,r){return async function(a,o){var i,l,u,d,m,h;const p=await s.stream(r,o==null?void 0:o.signal,o==null?void 0:o.timeoutMs,o==null?void 0:o.headers,a,o==null?void 0:o.contextValues);let f;(m=o==null?void 0:o.onHeader)===null||m===void 0||m.call(o,p.header);let b=0;try{for(var _,E=!0,C=Pa(p.message);!(i=(_=await C.next()).done);E=!0)d=_.value,E=!1,f=d,b++}catch(y){l={error:y}}finally{try{E||i||!(u=C.return)||await u.call(C)}finally{if(l)throw l.error}}if(!f)throw new we("protocol error: missing response message",Re.Unimplemented);if(b>1)throw new we("protocol error: received extra messages for client streaming method",Re.Unimplemented);return(h=o==null?void 0:o.onTrailer)===null||h===void 0||h.call(o,p.trailer),f}}(t,n);case"bidi_streaming":return function(s,r){return function(a,o){return mr(s.stream(r,o==null?void 0:o.signal,o==null?void 0:o.timeoutMs,o==null?void 0:o.headers,a,o==null?void 0:o.contextValues),o)}}(t,n);default:return null}})}function mr(e,t){const n=function(){return qi(this,arguments,function*(){var s,r;const a=yield Je(e);(s=t==null?void 0:t.onHeader)===null||s===void 0||s.call(t,a.header),yield Je(yield*Li(Pa(a.message))),(r=t==null?void 0:t.onTrailer)===null||r===void 0||r.call(t,a.trailer)})}()[Symbol.asyncIterator]();return{[Symbol.asyncIterator]:()=>({next:()=>n.next()})}}function Gi(e,t,...n){if(n.length>0)throw new Error;return e.services[t]}async function hr(e){const t=await crypto.subtle.digest("SHA-256",e);return Array.from(new Uint8Array(t)).map(n=>n.toString(16).padStart(2,"0")).join("")}var La=(e=>(e.chat="chat",e))(La||{}),qa=(e=>(e.chatMentionFolder="chat-mention-folder",e.chatMentionFile="chat-mention-file",e.chatMentionExternalSource="chat-mention-external-source",e.chatClearContext="chat-clear-context",e.chatRestoreDefaultContext="chat-restore-default-context",e.chatUseActionFind="chat-use-action-find",e.chatUseActionExplain="chat-use-action-explain",e.chatUseActionWriteTest="chat-use-action-write-test",e.chatNewConversation="chat-new-conversation",e.chatEditConversationName="chat-edit-conversation-name",e.chatFailedSmartPasteResolveFile="chat-failed-smart-paste-resolve-file",e.chatPrecomputeSmartPaste="chat-precompute-smart-paste",e.chatSmartPaste="chat-smart-paste",e.chatCodeblockCopy="chat-codeblock-copy",e.chatCodeblockCreate="chat-codeblock-create",e.chatCodeblockGoToFile="chat-codeblock-go-to-file",e.chatCodespanGoToFile="chat-codespan-go-to-file",e.chatCodespanGoToSymbol="chat-codespan-go-to-symbol",e.chatMermaidblockInitialize="chat-mermaidblock-initialize",e.chatMermaidblockToggle="chat-mermaidblock-toggle",e.chatMermaidblockInteract="chat-mermaidblock-interact",e.chatMermaidBlockError="chat-mermaidblock-error",e.chatUseSuggestedQuestion="chat-use-suggested-question",e.chatDisplaySuggestedQuestions="chat-display-suggested-questions",e.setWorkspaceGuidelines="chat-set-workspace-guidelines",e.clearWorkspaceGuidelines="chat-clear-workspace-guidelines",e.setUserGuidelines="chat-set-user-guidelines",e.clearUserGuidelines="chat-clear-user-guidelines",e))(qa||{});function pr(e){return e.replace(/^data:.*?;base64,/,"")}async function En(e){return new Promise((t,n)=>{const s=new FileReader;s.onload=r=>{var a;return t((a=r.target)==null?void 0:a.result)},s.onerror=n,s.readAsDataURL(e)})}async function Tn(e){return e.length<1e4?Promise.resolve(function(t){const n=atob(t);return Uint8Array.from(n,s=>s.codePointAt(0)||0)}(e)):new Promise((t,n)=>{const s=new Worker(URL.createObjectURL(new Blob([`
            self.onmessage = function(e) {
              try {
                const base64 = e.data;
                const binString = atob(base64);
                const bytes = new Uint8Array(binString.length);
                for (let i = 0; i < binString.length; i++) {
                  bytes[i] = binString.charCodeAt(i);
                }
                self.postMessage(bytes, [bytes.buffer]);
              } catch (error) {
                self.postMessage({ error: error.message });
              }
            };
            `],{type:"application/javascript"})));s.onmessage=function(r){r.data.error?n(new Error(r.data.error)):t(r.data),s.terminate()},s.onerror=function(r){n(r.error),s.terminate()},s.postMessage(e)})}const Vi=Gi(cs("Ci5jbGllbnRzL3NpZGVjYXIvbGlicy9wcm90b3MvdGVzdF9zZXJ2aWNlLnByb3RvEgR0ZXN0IhoKC1Rlc3RSZXF1ZXN0EgsKA2ZvbxgBIAEoCSIeCgxUZXN0UmVzcG9uc2USDgoGcmVzdWx0GAEgASgJMngKC1Rlc3RTZXJ2aWNlEjMKClRlc3RNZXRob2QSES50ZXN0LlRlc3RSZXF1ZXN0GhIudGVzdC5UZXN0UmVzcG9uc2USNAoLRXJyb3JNZXRob2QSES50ZXN0LlRlc3RSZXF1ZXN0GhIudGVzdC5UZXN0UmVzcG9uc2ViBnByb3RvMw"),0);var U=(e=>(e.getEditListRequest="agent-get-edit-list-request",e.getEditListResponse="agent-get-edit-list-response",e.getEditChangesByRequestIdRequest="agent-get-edit-changes-by-request-id-request",e.getEditChangesByRequestIdResponse="agent-get-edit-changes-by-request-id-response",e.setCurrentConversation="agent-set-current-conversation",e.migrateConversationId="agent-migrate-conversation-id",e.revertToTimestamp="revert-to-timestamp",e.chatAgentEditAcceptAll="chat-agent-edit-accept-all",e.reportAgentSessionEvent="report-agent-session-event",e.reportAgentRequestEvent="report-agent-request-event",e.chatReviewAgentFile="chat-review-agent-file",e.getAgentEditContentsByRequestId="get-agent-edit-contents-by-request-id",e.getAgentEditContentsByRequestIdResponse="get-agent-edit-contents-by-request-id-response",e.checkHasEverUsedAgent="check-has-ever-used-agent",e.checkHasEverUsedAgentResponse="check-has-ever-used-agent-response",e.setHasEverUsedAgent="set-has-ever-used-agent",e.checkHasEverUsedRemoteAgent="check-has-ever-used-remote-agent",e.checkHasEverUsedRemoteAgentResponse="check-has-ever-used-remote-agent-response",e.setHasEverUsedRemoteAgent="set-has-ever-used-remote-agent",e.getSoundSettings="get-sound-settings",e.getSoundSettingsResponse="get-sound-settings-response",e.updateSoundSettings="update-sound-settings",e.soundSettingsBroadcast="sound-settings-broadcast",e.getSwarmModeSettings="get-swarm-mode-settings",e.getSwarmModeSettingsResponse="get-swarm-mode-settings-response",e.updateSwarmModeSettings="update-swarm-mode-settings",e.swarmModeSettingsBroadcast="swarm-mode-settings-broadcast",e.getChatModeRequest="get-chat-mode-request",e.getChatModeResponse="get-chat-mode-response",e))(U||{}),Ft=(e=>(e.checkToolCallSafeRequest="check-tool-call-safe-request",e.checkToolCallSafeResponse="check-tool-call-safe-response",e.closeAllToolProcesses="close-all-tool-processes",e.getToolIdentifierRequest="get-tool-identifier-request",e.getToolIdentifierResponse="get-tool-identifier-response",e))(Ft||{}),Ut=(e=>(e.loadConversationExchangesRequest="load-conversation-exchanges-request",e.loadConversationExchangesResponse="load-conversation-exchanges-response",e.loadExchangesByUuidsRequest="load-exchanges-by-uuids-request",e.loadExchangesByUuidsResponse="load-exchanges-by-uuids-response",e.saveExchangesRequest="save-exchanges-request",e.saveExchangesResponse="save-exchanges-response",e.deleteExchangesRequest="delete-exchanges-request",e.deleteExchangesResponse="delete-exchanges-response",e.deleteConversationExchangesRequest="delete-conversation-exchanges-request",e.deleteConversationExchangesResponse="delete-conversation-exchanges-response",e.countExchangesRequest="count-exchanges-request",e.countExchangesResponse="count-exchanges-response",e))(Ut||{});class Bi{constructor(t=[]){c(this,"_items",[]);c(this,"_focusedItemIdx");c(this,"_subscribers",new Set);c(this,"subscribe",t=>(this._subscribers.add(t),t(this),()=>{this._subscribers.delete(t)}));c(this,"setItems",t=>{this._items=t,this._items.length===0?this.setFocusIdx(void 0):this._focusedItemIdx!==void 0&&this._focusedItemIdx>=this._items.length?this.setFocusIdx(this._items.length-1):this._focusedItemIdx===void 0?this.setFocusIdx(void 0):this.setFocusIdx(this._focusedItemIdx)});c(this,"setFocus",t=>{if(t!==void 0&&t===this.focusedItem)return;const n=t?this._items.indexOf(t):-1;n===-1?this.setFocusIdx(void 0):this.setFocusIdx(n)});c(this,"setFocusIdx",t=>{if(t===this._focusedItemIdx||this._items.length===0)return;if(t===void 0)return this._focusedItemIdx=void 0,void this.notifySubscribers();const n=Math.floor(t/this._items.length)*this._items.length;this._focusedItemIdx=(t-n)%this._items.length,this.notifySubscribers()});c(this,"initFocusIdx",t=>this._focusedItemIdx===void 0&&(this.setFocusIdx(t),!0));c(this,"focusNext",()=>{const t=this.nextIdx();if(t!==void 0)return this.setFocus(this._items[t]),t});c(this,"focusPrev",()=>{const t=this.prevIdx();if(t!==void 0)return this.setFocus(this._items[t]),t});c(this,"prevIdx",(t={})=>{if(this._items.length!==0)return this._focusedItemIdx===void 0?this._items.length-1:t.nowrap&&this._focusedItemIdx===0?0:(this._focusedItemIdx-1+this._items.length)%this._items.length});c(this,"nextIdx",(t={})=>{if(this._items.length!==0)return this._focusedItemIdx===void 0?0:t.nowrap&&this._focusedItemIdx===this._items.length-1?this._items.length-1:(this._focusedItemIdx+1)%this._items.length});c(this,"notifySubscribers",()=>{this._subscribers.forEach(t=>t(this))});this._items=t}get items(){return this._items}get focusedItem(){if(this._focusedItemIdx!==void 0)return this._items[this._focusedItemIdx]}get focusedItemIdx(){return this._focusedItemIdx}}var Yi=(e=>(e[e.unspecified=0]="unspecified",e[e.userGuidelines=1]="userGuidelines",e[e.augmentGuidelines=2]="augmentGuidelines",e[e.rules=3]="rules",e))(Yi||{}),ji=(e=>(e[e.unspecified=0]="unspecified",e[e.manuallyCreated=1]="manuallyCreated",e[e.auto=2]="auto",e[e.selectedDirectory=3]="selectedDirectory",e[e.selectedFile=4]="selectedFile",e))(ji||{});function Ki(e){return e===void 0?{num_lines:-1,num_chars:-1}:{num_lines:e.split(`
`).length,num_chars:e.length}}class Ha{constructor(){this.tracingData={flags:{},nums:{},string_stats:{},request_ids:{}}}setFlag(t,n=!0){this.tracingData.flags[t]={value:n,timestamp:new Date().toISOString()}}getFlag(t){const n=this.tracingData.flags[t];return n==null?void 0:n.value}setNum(t,n){this.tracingData.nums[t]={value:n,timestamp:new Date().toISOString()}}getNum(t){const n=this.tracingData.nums[t];return n==null?void 0:n.value}setStringStats(t,n){this.tracingData.string_stats[t]={value:Ki(n),timestamp:new Date().toISOString()}}setRequestId(t,n){this.tracingData.request_ids[t]={value:n,timestamp:new Date().toISOString()}}}var Wi=(e=>(e[e.unspecified=0]="unspecified",e[e.classify_and_distill=1]="classify_and_distill",e[e.orientation=2]="orientation",e))(Wi||{}),zi=(e=>(e.start="start",e.end="end",e.memoriesRequestId="memoriesRequestId",e.exceptionThrown="exceptionThrown",e.lastUserExchangeRequestId="lastUserExchangeRequestId",e.noMemoryData="noMemoryData",e.agenticTurnHasRememberToolCall="agenticTurnHasRememberToolCall",e.emptyMemory="emptyMemory",e.removeUserExchangeMemoryFailed="removeUserExchangeMemoryFailed",e))(zi||{});class Ga extends Ha{constructor(){super()}static create(){return new Ga}}var Xi=(e=>(e.openedAgentConversation="opened-agent-conversation",e.revertCheckpoint="revert-checkpoint",e.agentInterruption="agent-interruption",e.sentUserMessage="sent-user-message",e.rememberToolCall="remember-tool-call",e.openedMemoriesFile="opened-memories-file",e.initialOrientation="initial-orientation",e.classifyAndDistill="classify-and-distill",e.flushMemories="flush-memories",e.vsCodeTerminalShellIntegrationNotAvailable="vs-code-terminal-shell-integration-not-available",e.vsCodeTerminalReadingApproximateOutput="vs-code-terminal-reading-approximate-output",e.vsCodeTerminalTimedOutWaitingForNoopCommand="vs-code-terminal-timed-out-waiting-for-noop-command",e.vsCodeTerminalFailedToUseShellIntegration="vs-code-terminal-failed-to-use-shell-integration",e.vsCodeTerminalLastCommandIsSameAsCurrent="vs-code-terminal-last-command-is-same-as-current",e.vsCodeTerminalPollingDeterminedProcessIsDone="vs-code-terminal-polling-determined-process-is-done",e.vsCodeTerminalFailedToReadOutput="vs-code-terminal-failed-to-read-output",e.vsCodeTerminalBuggyOutput="vs-code-terminal-buggy-output",e.vsCodeTerminalBuggyExecutionEvents="vs-code-terminal-buggy-execution-events",e.vsCodeTerminalUnsupportedVSCodeShell="vs-code-terminal-unsupported-vscode-shell",e.vsCodeTerminalFailedToFindGitBash="vs-code-terminal-failed-to-find-git-bash",e.vsCodeTerminalFailedToFindPowerShell="vs-code-terminal-failed-to-find-powershell",e.vsCodeTerminalNoSupportedShellsFound="vs-code-terminal-no-supported-shells-found",e.vsCodeTerminalSettingsChanged="vs-code-terminal-settings-changed",e.vsCodeTerminalWaitTimeout="vs-code-terminal-wait-timeout",e.vsCodeTerminalErrorLoadingSettings="vs-code-terminal-error-loading-settings",e.vsCodeTerminalErrorCheckingForShellUpdates="vs-code-terminal-error-checking-for-shell-updates",e.vsCodeTerminalErrorCleaningUpTempDir="vs-code-terminal-error-cleaning-up-temp-dir",e.vsCodeTerminalErrorInitializingShells="vs-code-terminal-error-initializing-shells",e.vsCodeTerminalErrorCheckingShellCapability="vs-code-terminal-error-checking-shell-capability",e.vsCodeTerminalErrorCreatingZshEnvironment="vs-code-terminal-error-creating-zsh-environment",e.vsCodeTerminalMissedStartEvent="vs-code-terminal-missed-start-event",e.vsCodeTerminalReadStreamTimeoutWhenProcessIsComplete="vs-code-terminal-read-stream-timeout-when-process-is-complete",e.vsCodeTerminalScriptCommandNotAvailable="vs-code-terminal-script-command-not-available",e.enhancedPrompt="enhanced-prompt",e.memoriesMove="memories-move",e.rulesImported="rules-imported",e.taskListUsage="task-list-usage",e.memoryUsage="memory-usage",e.contentTruncation="content-truncation",e))(Xi||{}),Bt=(e=>(e.sentUserMessage="sent-user-message",e.chatHistorySummarization="chat-history-summarization",e.enhancedPrompt="enhanced-prompt",e.firstTokenReceived="first-token-received",e.chatHistoryTruncated="chat-history-truncated",e))(Bt||{}),Ji=(e=>(e.memoriesRequestId="memoriesRequestId",e.exceptionThrown="exceptionThrown",e.start="start",e.end="end",e.noPendingUserMessage="noPendingUserMessage",e.startSendSilentExchange="startSendSilentExchange",e.sendSilentExchangeRequestId="sendSilentExchangeRequestId",e.sendSilentExchangeResponseStats="sendSilentExchangeResponseStats",e.noRequestId="noRequestId",e.conversationChanged="conversationChanged",e.explanationStats="explanationStats",e.contentStats="contentStats",e.invalidResponse="invalidResponse",e.worthRemembering="worthRemembering",e.lastUserExchangeRequestId="lastUserExchangeRequestId",e.noLastUserExchangeRequestId="noLastUserExchangeRequestId",e))(Ji||{});class Va extends Ha{constructor(){super()}static create(){return new Va}}var Zi=(e=>(e.remoteAgentSetup="remote-agent-setup",e.setupScript="setup-script",e.sshInteraction="ssh-interaction",e.notificationBell="notification-bell",e.diffPanel="diff-panel",e.setupPageOpened="setup-page-opened",e.githubAPIFailure="github-api-failure",e.remoteAgentCreated="remote-agent-created",e.changesApplied="changes-applied",e.createdPR="created-pr",e.modeSelector="mode-selector",e.remoteAgentSetupWindow="remote-agent-setup-window",e.remoteAgentThreadList="remote-agent-thread-list",e.remoteAgentNewThreadButton="remote-agent-new-thread-button",e))(Zi||{}),Qi=(e=>(e[e.unknownSourceControl=0]="unknownSourceControl",e[e.git=1]="git",e[e.github=2]="github",e))(Qi||{}),el=(e=>(e[e.unknownMode=0]="unknownMode",e[e.chat=1]="chat",e[e.agent=2]="agent",e[e.remoteAgent=3]="remoteAgent",e))(el||{}),tl=(e=>(e[e.unknownModeSelectorAction=0]="unknownModeSelectorAction",e[e.open=1]="open",e[e.close=2]="close",e[e.select=3]="select",e[e.init=4]="init",e))(tl||{}),nl=(e=>(e[e.unknownSetupWindowAction=0]="unknownSetupWindowAction",e[e.open=1]="open",e[e.close=2]="close",e[e.selectRepo=3]="selectRepo",e[e.selectBranch=4]="selectBranch",e[e.selectSetupScript=5]="selectSetupScript",e[e.autoGenerateSetupScript=6]="autoGenerateSetupScript",e[e.manuallyCreateSetupScript=7]="manuallyCreateSetupScript",e[e.typeInPromptWindow=8]="typeInPromptWindow",e[e.clickRewritePrompt=9]="clickRewritePrompt",e[e.enableNotifications=10]="enableNotifications",e[e.disableNotifications=11]="disableNotifications",e[e.clickCreateAgent=12]="clickCreateAgent",e))(nl||{}),sl=(e=>(e[e.unknownAgentListAction=0]="unknownAgentListAction",e[e.open=1]="open",e[e.close=2]="close",e[e.selectAgent=3]="selectAgent",e[e.deleteAgent=4]="deleteAgent",e[e.pinAgent=5]="pinAgent",e[e.unpinAgent=6]="unpinAgent",e))(sl||{}),rl=(e=>(e[e.unknown=0]="unknown",e[e.click=1]="click",e[e.open=2]="open",e[e.close=3]="close",e))(rl||{}),al=(e=>(e[e.unknown=0]="unknown",e[e.chat=1]="chat",e[e.agent=2]="agent",e[e.remoteAgent=3]="remoteAgent",e))(al||{}),ol=(e=>(e[e.unknown=0]="unknown",e[e.addTask=1]="addTask",e[e.addSubtask=2]="addSubtask",e[e.updateTaskStatus=3]="updateTaskStatus",e[e.updateTaskName=4]="updateTaskName",e[e.updateTaskDescription=5]="updateTaskDescription",e[e.reorganizeTaskList=6]="reorganizeTaskList",e[e.deleteTask=7]="deleteTask",e[e.runSingleTask=8]="runSingleTask",e[e.runAllTasks=9]="runAllTasks",e[e.viewTaskList=10]="viewTaskList",e[e.exportTaskList=11]="exportTaskList",e[e.importTaskList=12]="importTaskList",e[e.syncTaskList=13]="syncTaskList",e))(ol||{}),il=(e=>(e[e.unknown=0]="unknown",e[e.user=1]="user",e[e.agent=2]="agent",e))(il||{}),ll=(e=>(e[e.unknown=0]="unknown",e[e.saveMemory=1]="saveMemory",e[e.discardMemory=2]="discardMemory",e[e.editMemory=3]="editMemory",e[e.viewMemories=4]="viewMemories",e[e.refreshMemories=5]="refreshMemories",e[e.filterByState=6]="filterByState",e[e.filterByVersion=7]="filterByVersion",e[e.openMemoriesFile=8]="openMemoriesFile",e[e.createMemory=9]="createMemory",e))(ll||{}),ul=(e=>(e[e.unknown=0]="unknown",e[e.user=1]="user",e[e.agent=2]="agent",e))(ul||{});function cl(e,t,n=1e3){let s=null,r=0;const a=Yn(t),o=()=>{const i=(()=>{const l=Date.now();if(s!==null&&l-r<n)return s;const u=e();return s=u,r=l,u})();a.set(i)};return{subscribe:a.subscribe,resetCache:()=>{s=null,o()},updateStore:o}}var Ba=(e=>(e[e.unset=0]="unset",e[e.positive=1]="positive",e[e.negative=2]="negative",e))(Ba||{}),dl=(e=>(e.longRunning="longRunning",e.running="running",e.done="done",e))(dl||{}),ml=(e=>(e.initializing="initializing",e.enabled="enabled",e.disabled="disabled",e.partial="partial",e))(ml||{});const fs=class fs{static hasFrontmatter(t){return this.frontmatterRegex.test(t)}static extractFrontmatter(t){const n=t.match(this.frontmatterRegex);return n&&n[1]?n[1]:null}static extractContent(t){return t.replace(this.frontmatterRegex,"")}static parseBoolean(t,n,s=!0){const r=this.extractFrontmatter(t);if(r){const a=new RegExp(`${n}\\s*:\\s*(true|false)`,"i"),o=r.match(a);if(o&&o[1])return o[1].toLowerCase()==="true"}return s}static parseString(t,n,s=""){const r=this.extractFrontmatter(t);if(r){const a=new RegExp(`${n}\\s*:\\s*["']?([^"'
]*)["']?`,"i"),o=r.match(a);if(o&&o[1])return o[1].trim()}return s}static updateFrontmatter(t,n,s){const r=t.match(this.frontmatterRegex),a=typeof s!="string"||/^(true|false)$/.test(s.toLowerCase())?String(s):`"${s}"`;if(r){const o=r[1],i=new RegExp(`(${n}\\s*:\\s*)([^\\n]*)`,"i");if(o.match(i)){const l=o.replace(i,`$1${a}`);return t.replace(this.frontmatterRegex,`---
${l}---
`)}{const l=`${o.endsWith(`
`)?o:o+`
`}${n}: ${a}
`;return t.replace(this.frontmatterRegex,`---
${l}---
`)}}return`---
${n}: ${a}
---

${t}`}static createFrontmatter(t,n){let s=t;this.hasFrontmatter(s)&&(s=this.extractContent(s));for(const[r,a]of Object.entries(n))s=this.updateFrontmatter(s,r,a);return s}};fs.frontmatterRegex=/^---\s*\n([\s\S]*?)\n---\s*\n/;let te=fs;const Fe=class Fe{static parseRuleFile(t,n){const s=te.parseString(t,this.DESCRIPTION_FRONTMATTER_KEY,""),r=te.extractContent(t);return{type:this.getRuleTypeFromContent(t),path:n,content:r,description:s||void 0}}static formatRuleFileForMarkdown(t){let n=t.content;return n=te.updateFrontmatter(n,this.TYPE_FRONTMATTER_KEY,this.mapRuleTypeToString(t.type)),t.description&&(n=te.updateFrontmatter(n,this.DESCRIPTION_FRONTMATTER_KEY,t.description)),n}static getAlwaysApplyFrontmatterKey(t){return te.parseBoolean(t,this.ALWAYS_APPLY_FRONTMATTER_KEY,!1)}static extractContent(t){return te.extractContent(t)}static updateAlwaysApplyFrontmatterKey(t,n){return te.updateFrontmatter(t,this.ALWAYS_APPLY_FRONTMATTER_KEY,n)}static getDescriptionFrontmatterKey(t){return te.parseString(t,this.DESCRIPTION_FRONTMATTER_KEY,"")}static updateDescriptionFrontmatterKey(t,n){return te.updateFrontmatter(t,this.DESCRIPTION_FRONTMATTER_KEY,n)}static mapStringToRuleType(t){switch(t.toLowerCase()){case"always_apply":return be.ALWAYS_ATTACHED;case"manual":return be.MANUAL;case"agent_requested":return be.AGENT_REQUESTED;default:return this.DEFAULT_RULE_TYPE}}static mapRuleTypeToString(t){switch(t){case be.ALWAYS_ATTACHED:return"always_apply";case be.MANUAL:return"manual";case be.AGENT_REQUESTED:return"agent_requested";default:return"manual"}}static isValidTypeValue(t){return this.VALID_TYPE_VALUES.includes(t.toLowerCase())}static getTypeFrontmatterKey(t){return te.parseString(t,this.TYPE_FRONTMATTER_KEY,"")}static updateTypeFrontmatterKey(t,n){const s=this.mapRuleTypeToString(n);return te.updateFrontmatter(t,this.TYPE_FRONTMATTER_KEY,s)}static getRuleTypeFromContent(t){const n=this.getTypeFrontmatterKey(t);if(n&&this.isValidTypeValue(n))return this.mapStringToRuleType(n);const s=this.getAlwaysApplyFrontmatterKey(t),r=this.getDescriptionFrontmatterKey(t);return s?be.ALWAYS_ATTACHED:r&&r.trim()!==""?be.AGENT_REQUESTED:be.MANUAL}};Fe.ALWAYS_APPLY_FRONTMATTER_KEY="alwaysApply",Fe.DESCRIPTION_FRONTMATTER_KEY="description",Fe.TYPE_FRONTMATTER_KEY="type",Fe.VALID_TYPE_VALUES=["always_apply","manual","agent_requested"],Fe.DEFAULT_RULE_TYPE=be.MANUAL;let gr=Fe;const hl=".augment",pl="rules",Uc=".augment-guidelines";function W(e,t){return t in e&&e[t]!==void 0}function gl(e){return W(e,"file")}function fl(e){return W(e,"recentFile")}function yl(e){return W(e,"folder")}function _l(e){return W(e,"sourceFolder")}function Pc(e){return W(e,"sourceFolderGroup")}function Lc(e){return W(e,"selection")}function bl(e){return W(e,"externalSource")}function qc(e){return W(e,"allDefaultContext")}function Hc(e){return W(e,"clearContext")}function Gc(e){return W(e,"userGuidelines")}function Vc(e){return W(e,"agentMemories")}function Ya(e){return W(e,"personality")}function vl(e){return W(e,"rule")}function El(e){return W(e,"task")}const Bc={allDefaultContext:!0,label:"Default Context",id:"allDefaultContext"},Yc={clearContext:!0,label:"Clear Context",id:"clearContext"},jc={userGuidelines:{overLimit:!1,contents:"",lengthLimit:2e3},label:"User Guidelines",id:"userGuidelines"},Kc={agentMemories:{},label:"Agent Memories",id:"agentMemories"},fr=[{personality:{type:q.DEFAULT,description:"Expert software engineer - trusted coding agent, at your service!"},label:"Agent Auggie",name:"auggie-personality-agent-default",id:"auggie-personality-agent-default"},{personality:{type:q.PROTOTYPER,description:"Fast and loose - let's get it done, boss!"},label:"Prototyper Auggie",name:"auggie-personality-prototyper",id:"auggie-personality-prototyper"},{personality:{type:q.BRAINSTORM,description:"Thoughtful and creative - thinking through all possibilities..."},label:"Brainstorm Auggie",name:"auggie-personality-brainstorm",id:"auggie-personality-brainstorm"},{personality:{type:q.REVIEWER,description:"Code detective - finding issues and analyzing implications"},label:"Reviewer Auggie",name:"auggie-personality-reviewer",id:"auggie-personality-reviewer"}];function Wc(e){return W(e,"group")}function zc(e){const t=new Map;return e.forEach(n=>{gl(n)?t.set("file",[...t.get("file")??[],n]):fl(n)?t.set("recentFile",[...t.get("recentFile")??[],n]):yl(n)?t.set("folder",[...t.get("folder")??[],n]):bl(n)?t.set("externalSource",[...t.get("externalSource")??[],n]):_l(n)?t.set("sourceFolder",[...t.get("sourceFolder")??[],n]):Ya(n)?t.set("personality",[...t.get("personality")??[],n]):vl(n)&&t.set("rule",[...t.get("rule")??[],n])}),[{label:"Personalities",id:"personalities",group:{type:"personality",materialIcon:"person",items:t.get("personality")??[]}},{label:"Files",id:"files",group:{type:"file",materialIcon:"insert_drive_file",items:t.get("file")??[]}},{label:"Folders",id:"folders",group:{type:"folder",materialIcon:"folder",items:t.get("folder")??[]}},{label:"Source Folders",id:"sourceFolders",group:{type:"sourceFolder",materialIcon:"folder_managed",items:t.get("sourceFolder")??[]}},{label:"Recently Opened Files",id:"recentlyOpenedFiles",group:{type:"recentFile",materialIcon:"insert_drive_file",items:t.get("recentFile")??[]}},{label:"Documentation",id:"externalSources",group:{type:"externalSource",materialIcon:"link",items:t.get("externalSource")??[]}},{label:"Rules",id:"rules",group:{type:"rule",materialIcon:"rule",items:t.get("rule")??[]}}].filter(n=>n.group.items.length>0)}function Tl(e){const t=(n={rootPath:e.repoRoot,relPath:e.pathName}).rootPath+"/"+n.relPath;var n;const s={label:fo(e.pathName).split("/").filter(r=>r.trim()!=="").pop()||"",name:t,id:t};if(e.fullRange){const r=`:L${e.fullRange.startLineNumber}-${e.fullRange.endLineNumber}`;s.label+=r,s.name+=r,s.id+=r}else if(e.range){const r=`:L${e.range.start}-${e.range.stop}`;s.label+=r,s.name+=r,s.id+=r}return s}function Sl(e){const t=e.path.split("/"),n=t[t.length-1],s=n.endsWith(".md")?n.slice(0,-3):n,r=`${hl}/${pl}/${e.path}`;return{label:s,name:r,id:r}}var x=(e=>(e[e.unknown=0]="unknown",e[e.new=1]="new",e[e.checkingSafety=2]="checkingSafety",e[e.runnable=3]="runnable",e[e.running=4]="running",e[e.completed=5]="completed",e[e.error=6]="error",e[e.cancelling=7]="cancelling",e[e.cancelled=8]="cancelled",e))(x||{});function Sn(e){return`${e.requestId};${e.toolUseId}`}function yr(e){const[t,n]=e.split(";");return{requestId:t,toolUseId:n}}var Il=(e=>(e.readFile="read-file",e.saveFile="save-file",e.editFile="edit-file",e.clarify="clarify",e.onboardingSubAgent="onboarding-sub-agent",e.launchProcess="launch-process",e.killProcess="kill-process",e.readProcess="read-process",e.writeProcess="write-process",e.listProcesses="list-processes",e.waitProcess="wait-process",e.openBrowser="open-browser",e.strReplaceEditor="str-replace-editor",e.remember="remember",e.diagnostics="diagnostics",e.setupScript="setup-script",e.readTerminal="read-terminal",e.gitCommitRetrieval="git-commit-retrieval",e.memoryRetrieval="memory-retrieval",e.startWorkerAgent="start_worker_agent",e.readWorkerState="read_worker_state",e.waitForWorkerAgent="wait_for_worker_agent",e.sendInstructionToWorkerAgent="send_instruction_to_worker_agent",e.stopWorkerAgent="stop_worker_agent",e.deleteWorkerAgent="delete_worker_agent",e.readWorkerAgentEdits="read_worker_agent_edits",e.applyWorkerAgentEdits="apply_worker_agent_edits",e.LocalSubAgent="local-sub-agent",e))(Il||{}),Nl=(e=>(e.remoteToolHost="remoteToolHost",e.localToolHost="localToolHost",e.sidecarToolHost="sidecarToolHost",e.mcpHost="mcpHost",e))(Nl||{}),Yt=(e=>(e[e.ContentText=0]="ContentText",e[e.ContentImage=1]="ContentImage",e))(Yt||{}),wl=(e=>(e[e.Unsafe=0]="Unsafe",e[e.Safe=1]="Safe",e[e.Check=2]="Check",e))(wl||{}),xl=(e=>(e[e.Unknown=0]="Unknown",e[e.WebSearch=1]="WebSearch",e[e.GitHubApi=8]="GitHubApi",e[e.Linear=12]="Linear",e[e.Jira=13]="Jira",e[e.Confluence=14]="Confluence",e[e.Notion=15]="Notion",e[e.Supabase=16]="Supabase",e[e.Glean=17]="Glean",e))(xl||{});function _r(e,t){return function(n,s){if(n.length<=s||n.length===0)return{truncatedText:n};const r=n.split(`
`),a="... additional lines truncated ..."+(r[0].endsWith("\r")?"\r":"");let o,i="";if(r.length<2||r[0].length+r[r.length-1].length+a.length>s){const l=Math.floor(s/2);i=[n.slice(0,l),"<...>",n.slice(-l)].join(""),o=[1,1,r.length,r.length]}else{const l=[],u=[];let d=a.length+1;for(let m=0;m<Math.floor(r.length/2);m++){const h=r[m],p=r[r.length-1-m],f=h.length+p.length+2;if(d+f>s)break;d+=f,l.push(h),u.push(p)}o=[1,l.length,r.length-u.length+1,r.length],l.push(a),l.push(...u.reverse()),i=l.join(`
`)}return{truncatedText:i,shownRangeWhenTruncated:o}}(e,t).truncatedText}function Cl(e){var n;if(!e)return Tt.IMAGE_FORMAT_UNSPECIFIED;switch((n=e.split("/")[1])==null?void 0:n.toLowerCase()){case"jpeg":case"jpg":return Tt.JPEG;case"png":return Tt.PNG;default:return Tt.IMAGE_FORMAT_UNSPECIFIED}}function kl(e,t,n){var r,a;if(e.phase!==x.cancelled&&e.phase!==x.completed&&e.phase!==x.error)return;let s;return(r=e.result)!=null&&r.contentNodes?(s=function(o,i){return o.map(l=>l.type===Yt.ContentText?{type:dn.CONTENT_TEXT,text_content:l.text_content}:l.type===Yt.ContentImage&&l.image_content&&i?{type:dn.CONTENT_IMAGE,image_content:{image_data:l.image_content.image_data,format:Cl(l.image_content.media_type)}}:{type:dn.CONTENT_TEXT,text_content:"[Error: Invalid content node]"})}(e.result.contentNodes,n),{content:"",is_error:e.result.isError,request_id:e.result.requestId,tool_use_id:t,content_nodes:s}):((a=e.result)==null?void 0:a.text)!==void 0?{content:e.result.text,is_error:e.result.isError,request_id:e.result.requestId,tool_use_id:t}:void 0}function Rl(e=[]){let t;for(const n of e){if(n.type===R.TOOL_USE)return n;n.type===R.TOOL_USE_START&&(t=n)}return t}function Al(e,t,n,s){if(!e||!t)return[];let r=!1;return t.filter(a=>{var i;const o=s!=null&&s.isActive&&a.tool_use?s.getToolUseState(a.tool_use.tool_use_id):n.getToolUseState(a.requestId??e,(i=a.tool_use)==null?void 0:i.tool_use_id);return r===!1&&o.phase!==x.new&&o.phase!==x.unknown&&o.phase!==x.checkingSafety&&a.tool_use!==void 0||(o.phase===x.runnable&&(r=!0),!1)})}function Xc(e,t){if(e.contentNodes&&e.contentNodes.length>0){const n=e.contentNodes.map(s=>{if(s.type===Yt.ContentText){let r="";return s.text_content&&(r=_r(s.text_content,t/e.contentNodes.length)),{...s,text_content:r}}return s});return{...e,contentNodes:n}}return{...e,text:_r(e.text,t)}}const Ol="__NEW_AGENT__",Jc=e=>e.chatItemType===void 0,Zc=(e,t)=>{var a;const n=e.chatHistory.at(-1);if(!n||!$(n))return $e.notRunning;if(!(n.status===I.success||n.status===I.failed||n.status===I.cancelled))return $e.running;const s=((a=n.structured_output_nodes)==null?void 0:a.filter(o=>o.type===R.TOOL_USE&&!!o.tool_use))??[];let r;if(r=t.enableParallelTools?Al(n.request_id,s,e).at(-1):s.at(-1),!r||!r.tool_use)return $e.notRunning;switch(e.getToolUseState(n.request_id,r.tool_use.tool_use_id).phase){case x.runnable:return $e.awaitingUserAction;case x.cancelled:return $e.notRunning;default:return $e.running}},Hn=e=>$(e)&&!!e.request_message,Ml=e=>e.chatHistory.findLast(t=>Hn(t)),Qc=(e,t)=>{const n=Ml(e);return n!=null&&n.request_id?e.historyFrom(n.request_id,!0).filter(s=>$(s)&&(!t||t(s))):[]},ed=e=>{var s;const t=e.chatHistory.at(-1);if(!(t!=null&&t.request_id)||!$(t))return!1;const n=((s=t.structured_output_nodes)==null?void 0:s.filter(r=>r.type===R.TOOL_USE))??[];for(const r of n)if(r.tool_use&&e.getToolUseState(t.request_id,r.tool_use.tool_use_id).phase===x.runnable)return e.updateToolUseState({requestId:t.request_id,toolUseId:r.tool_use.tool_use_id,phase:x.cancelled}),!0;return!1};function Dl(e,t){const n=e.customPersonalityPrompts;if(n)switch(t){case q.DEFAULT:if(n.agent&&n.agent.trim()!=="")return n.agent;break;case q.PROTOTYPER:if(n.prototyper&&n.prototyper.trim()!=="")return n.prototyper;break;case q.BRAINSTORM:if(n.brainstorm&&n.brainstorm.trim()!=="")return n.brainstorm;break;case q.REVIEWER:if(n.reviewer&&n.reviewer.trim()!=="")return n.reviewer}return $l[t]}const $l={[q.DEFAULT]:`
# Agent Auggie Personality Description
You are Augment Agent, an agentic coding AI assistant.
Focus on helping the user with their coding tasks efficiently.

## Rules:
- You have no restrictions on the tools you may use
- Follow the original system instructions
  `,[q.PROTOTYPER]:`
# Prototyper Auggie Personality Description
You are Prototyper Auggie, an agentic coding AI assistant focused on building prototypes and visual applications.

## Your approach:
- Be fast and action-oriented
- Implement things quickly to show results
- Open webpages to demonstrate functionality
- Focus on building something visual and interactive
- Use modern frameworks and tools to create working prototypes
- Prioritize getting a working demo over perfect architecture
- Show progress frequently with visual results
- Prefer to act and run tools, rather than asking for permission
- Only ask for permission if there is something potentially very dangerous or irreversible

## Implementation preferences:
- When user does not specify which frameworks to use, default to modern frameworks, e.g. React with vite or next.js
- Initialize projects using CLI tools instead of writing from scratch
- For database and auth, use Supabase as a good default option
- Before using open-browser to show the app, use curl to check for errors
- Remember that modern frameworks have hot reload, so avoid calling open-browser multiple times

## Rules:
- For extremely destructive or irreversible actions, you should ask for permission
- For other tasks, you must proceed without asking for permission
  `,[q.BRAINSTORM]:`
# Brainstorm Auggie Personality Description
You are Brainstorm Auggie, an agentic coding AI assistant focused on planning and brainstorming solutions.

## Your approach:
- Be slow, careful, and thorough in your analysis
- Look through all upstream/downstream APIs to understand implications
- Focus on finding a comprehensive plan that solves the user's query
- Do not run commands, create code, or implement solutions directly
- Your job is to be introspective and think deeply about the problem
- Brainstorm multiple approaches and evaluate their tradeoffs
- Consider edge cases and potential issues with each approach

## Planning preferences:
- Analyze the codebase thoroughly before suggesting changes
- Consider multiple implementation options with pros and cons
- Identify potential risks and challenges for each approach
- Create detailed, step-by-step plans for implementation
- Provide reasoning for architectural decisions
- Consider performance, maintainability, and scalability
- Do not execute the plan - your role is to provide guidance only

## Rules:
- Prefer information gathering and non-destructive tools
- Prefer non-destructive and non-modifying tools
- You must never execute code, modify the codebase, or make changes
- Consider using Mermaid diagrams to help visualize complex concepts
- Once you have a proposal, please examine it critically, and do a revision before finalizing
  `,[q.REVIEWER]:`
# Reviewer Auggie Personality Description
You are Reviewer Auggie, an agentic coding AI assistant focused on reviewing code changes and identifying potential issues.

## Your approach:
- Act like a code detective to find potential bugs and issues
- Use git commands to analyze changes against the merge base
- Be super inquisitive and look for anything suspicious
- Build a mental model of what is happening in the code change
- Analyze API implications and downstream effects
- Guard the codebase from potential negative side effects
- Focus on understanding the changes from first principles

## Review preferences:
- Use git and GitHub tools to get code history information
- Compare changes against the logical base or merge base
- Look for edge cases and potential bugs
- Analyze API contracts and potential breaking changes
- Consider performance implications
- Check for security vulnerabilities
- Verify test coverage for the changes

## Rules:
- Use git commands and GitHub API to analyze code changes
- Be thorough and methodical in your analysis
- Focus on finding potential issues rather than implementing solutions
- Provide constructive feedback with specific examples
- Consider both the technical implementation and the broader impact
  `};var ae=(e=>(e.NOT_STARTED="NOT_STARTED",e.IN_PROGRESS="IN_PROGRESS",e.CANCELLED="CANCELLED",e.COMPLETE="COMPLETE",e))(ae||{}),ps=(e=>(e.USER="USER",e.AGENT="AGENT",e))(ps||{}),ja={},jt={},Kt={};let xt;Object.defineProperty(Kt,"__esModule",{value:!0}),Kt.default=function(){if(!xt&&(xt=typeof crypto<"u"&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!xt))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return xt(Fl)};const Fl=new Uint8Array(16);var Ae={},qe={},Wt={};Object.defineProperty(Wt,"__esModule",{value:!0}),Wt.default=void 0;Wt.default=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i,Object.defineProperty(qe,"__esModule",{value:!0}),qe.default=void 0;var Ct,Ul=(Ct=Wt)&&Ct.__esModule?Ct:{default:Ct},Pl=function(e){return typeof e=="string"&&Ul.default.test(e)};qe.default=Pl,Object.defineProperty(Ae,"__esModule",{value:!0}),Ae.default=void 0,Ae.unsafeStringify=Ka;var Ll=function(e){return e&&e.__esModule?e:{default:e}}(qe);const P=[];for(let e=0;e<256;++e)P.push((e+256).toString(16).slice(1));function Ka(e,t=0){return P[e[t+0]]+P[e[t+1]]+P[e[t+2]]+P[e[t+3]]+"-"+P[e[t+4]]+P[e[t+5]]+"-"+P[e[t+6]]+P[e[t+7]]+"-"+P[e[t+8]]+P[e[t+9]]+"-"+P[e[t+10]]+P[e[t+11]]+P[e[t+12]]+P[e[t+13]]+P[e[t+14]]+P[e[t+15]]}var ql=function(e,t=0){const n=Ka(e,t);if(!(0,Ll.default)(n))throw TypeError("Stringified UUID is invalid");return n};Ae.default=ql,Object.defineProperty(jt,"__esModule",{value:!0}),jt.default=void 0;var Hl=function(e){return e&&e.__esModule?e:{default:e}}(Kt),Gl=Ae;let br,In,Nn=0,wn=0;var Vl=function(e,t,n){let s=t&&n||0;const r=t||new Array(16);let a=(e=e||{}).node||br,o=e.clockseq!==void 0?e.clockseq:In;if(a==null||o==null){const h=e.random||(e.rng||Hl.default)();a==null&&(a=br=[1|h[0],h[1],h[2],h[3],h[4],h[5]]),o==null&&(o=In=16383&(h[6]<<8|h[7]))}let i=e.msecs!==void 0?e.msecs:Date.now(),l=e.nsecs!==void 0?e.nsecs:wn+1;const u=i-Nn+(l-wn)/1e4;if(u<0&&e.clockseq===void 0&&(o=o+1&16383),(u<0||i>Nn)&&e.nsecs===void 0&&(l=0),l>=1e4)throw new Error("uuid.v1(): Can't create more than 10M uuids/sec");Nn=i,wn=l,In=o,i+=122192928e5;const d=(1e4*(268435455&i)+l)%4294967296;r[s++]=d>>>24&255,r[s++]=d>>>16&255,r[s++]=d>>>8&255,r[s++]=255&d;const m=i/4294967296*1e4&268435455;r[s++]=m>>>8&255,r[s++]=255&m,r[s++]=m>>>24&15|16,r[s++]=m>>>16&255,r[s++]=o>>>8|128,r[s++]=255&o;for(let h=0;h<6;++h)r[s+h]=a[h];return t||(0,Gl.unsafeStringify)(r)};jt.default=Vl;var zt={},Ce={},ft={};Object.defineProperty(ft,"__esModule",{value:!0}),ft.default=void 0;var Bl=function(e){return e&&e.__esModule?e:{default:e}}(qe),Yl=function(e){if(!(0,Bl.default)(e))throw TypeError("Invalid UUID");let t;const n=new Uint8Array(16);return n[0]=(t=parseInt(e.slice(0,8),16))>>>24,n[1]=t>>>16&255,n[2]=t>>>8&255,n[3]=255&t,n[4]=(t=parseInt(e.slice(9,13),16))>>>8,n[5]=255&t,n[6]=(t=parseInt(e.slice(14,18),16))>>>8,n[7]=255&t,n[8]=(t=parseInt(e.slice(19,23),16))>>>8,n[9]=255&t,n[10]=(t=parseInt(e.slice(24,36),16))/1099511627776&255,n[11]=t/4294967296&255,n[12]=t>>>24&255,n[13]=t>>>16&255,n[14]=t>>>8&255,n[15]=255&t,n};ft.default=Yl,Object.defineProperty(Ce,"__esModule",{value:!0}),Ce.URL=Ce.DNS=void 0,Ce.default=function(e,t,n){function s(r,a,o,i){var l;if(typeof r=="string"&&(r=function(d){d=unescape(encodeURIComponent(d));const m=[];for(let h=0;h<d.length;++h)m.push(d.charCodeAt(h));return m}(r)),typeof a=="string"&&(a=(0,Kl.default)(a)),((l=a)===null||l===void 0?void 0:l.length)!==16)throw TypeError("Namespace must be array-like (16 iterable integer values, 0-255)");let u=new Uint8Array(16+r.length);if(u.set(a),u.set(r,a.length),u=n(u),u[6]=15&u[6]|t,u[8]=63&u[8]|128,o){i=i||0;for(let d=0;d<16;++d)o[i+d]=u[d];return o}return(0,jl.unsafeStringify)(u)}try{s.name=e}catch{}return s.DNS=Wa,s.URL=za,s};var jl=Ae,Kl=function(e){return e&&e.__esModule?e:{default:e}}(ft);const Wa="6ba7b810-9dad-11d1-80b4-00c04fd430c8";Ce.DNS=Wa;const za="6ba7b811-9dad-11d1-80b4-00c04fd430c8";Ce.URL=za;var Xt={};function vr(e){return 14+(e+64>>>9<<4)+1}function ke(e,t){const n=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(n>>16)<<16|65535&n}function an(e,t,n,s,r,a){return ke((o=ke(ke(t,e),ke(s,a)))<<(i=r)|o>>>32-i,n);var o,i}function V(e,t,n,s,r,a,o){return an(t&n|~t&s,e,t,r,a,o)}function B(e,t,n,s,r,a,o){return an(t&s|n&~s,e,t,r,a,o)}function Y(e,t,n,s,r,a,o){return an(t^n^s,e,t,r,a,o)}function j(e,t,n,s,r,a,o){return an(n^(t|~s),e,t,r,a,o)}Object.defineProperty(Xt,"__esModule",{value:!0}),Xt.default=void 0;var Wl=function(e){if(typeof e=="string"){const t=unescape(encodeURIComponent(e));e=new Uint8Array(t.length);for(let n=0;n<t.length;++n)e[n]=t.charCodeAt(n)}return function(t){const n=[],s=32*t.length,r="0123456789abcdef";for(let a=0;a<s;a+=8){const o=t[a>>5]>>>a%32&255,i=parseInt(r.charAt(o>>>4&15)+r.charAt(15&o),16);n.push(i)}return n}(function(t,n){t[n>>5]|=128<<n%32,t[vr(n)-1]=n;let s=1732584193,r=-271733879,a=-1732584194,o=271733878;for(let i=0;i<t.length;i+=16){const l=s,u=r,d=a,m=o;s=V(s,r,a,o,t[i],7,-680876936),o=V(o,s,r,a,t[i+1],12,-389564586),a=V(a,o,s,r,t[i+2],17,606105819),r=V(r,a,o,s,t[i+3],22,-1044525330),s=V(s,r,a,o,t[i+4],7,-176418897),o=V(o,s,r,a,t[i+5],12,1200080426),a=V(a,o,s,r,t[i+6],17,-1473231341),r=V(r,a,o,s,t[i+7],22,-45705983),s=V(s,r,a,o,t[i+8],7,1770035416),o=V(o,s,r,a,t[i+9],12,-1958414417),a=V(a,o,s,r,t[i+10],17,-42063),r=V(r,a,o,s,t[i+11],22,-1990404162),s=V(s,r,a,o,t[i+12],7,1804603682),o=V(o,s,r,a,t[i+13],12,-40341101),a=V(a,o,s,r,t[i+14],17,-1502002290),r=V(r,a,o,s,t[i+15],22,1236535329),s=B(s,r,a,o,t[i+1],5,-165796510),o=B(o,s,r,a,t[i+6],9,-1069501632),a=B(a,o,s,r,t[i+11],14,643717713),r=B(r,a,o,s,t[i],20,-373897302),s=B(s,r,a,o,t[i+5],5,-701558691),o=B(o,s,r,a,t[i+10],9,38016083),a=B(a,o,s,r,t[i+15],14,-660478335),r=B(r,a,o,s,t[i+4],20,-405537848),s=B(s,r,a,o,t[i+9],5,568446438),o=B(o,s,r,a,t[i+14],9,-1019803690),a=B(a,o,s,r,t[i+3],14,-187363961),r=B(r,a,o,s,t[i+8],20,1163531501),s=B(s,r,a,o,t[i+13],5,-1444681467),o=B(o,s,r,a,t[i+2],9,-51403784),a=B(a,o,s,r,t[i+7],14,1735328473),r=B(r,a,o,s,t[i+12],20,-1926607734),s=Y(s,r,a,o,t[i+5],4,-378558),o=Y(o,s,r,a,t[i+8],11,-2022574463),a=Y(a,o,s,r,t[i+11],16,1839030562),r=Y(r,a,o,s,t[i+14],23,-35309556),s=Y(s,r,a,o,t[i+1],4,-1530992060),o=Y(o,s,r,a,t[i+4],11,1272893353),a=Y(a,o,s,r,t[i+7],16,-155497632),r=Y(r,a,o,s,t[i+10],23,-1094730640),s=Y(s,r,a,o,t[i+13],4,681279174),o=Y(o,s,r,a,t[i],11,-358537222),a=Y(a,o,s,r,t[i+3],16,-722521979),r=Y(r,a,o,s,t[i+6],23,76029189),s=Y(s,r,a,o,t[i+9],4,-640364487),o=Y(o,s,r,a,t[i+12],11,-421815835),a=Y(a,o,s,r,t[i+15],16,530742520),r=Y(r,a,o,s,t[i+2],23,-995338651),s=j(s,r,a,o,t[i],6,-198630844),o=j(o,s,r,a,t[i+7],10,1126891415),a=j(a,o,s,r,t[i+14],15,-1416354905),r=j(r,a,o,s,t[i+5],21,-57434055),s=j(s,r,a,o,t[i+12],6,1700485571),o=j(o,s,r,a,t[i+3],10,-1894986606),a=j(a,o,s,r,t[i+10],15,-1051523),r=j(r,a,o,s,t[i+1],21,-2054922799),s=j(s,r,a,o,t[i+8],6,1873313359),o=j(o,s,r,a,t[i+15],10,-30611744),a=j(a,o,s,r,t[i+6],15,-1560198380),r=j(r,a,o,s,t[i+13],21,1309151649),s=j(s,r,a,o,t[i+4],6,-145523070),o=j(o,s,r,a,t[i+11],10,-1120210379),a=j(a,o,s,r,t[i+2],15,718787259),r=j(r,a,o,s,t[i+9],21,-343485551),s=ke(s,l),r=ke(r,u),a=ke(a,d),o=ke(o,m)}return[s,r,a,o]}(function(t){if(t.length===0)return[];const n=8*t.length,s=new Uint32Array(vr(n));for(let r=0;r<n;r+=8)s[r>>5]|=(255&t[r/8])<<r%32;return s}(e),8*e.length))};Xt.default=Wl,Object.defineProperty(zt,"__esModule",{value:!0}),zt.default=void 0;var zl=Xa(Ce),Xl=Xa(Xt);function Xa(e){return e&&e.__esModule?e:{default:e}}var Jl=(0,zl.default)("v3",48,Xl.default);zt.default=Jl;var Jt={},Zt={};Object.defineProperty(Zt,"__esModule",{value:!0}),Zt.default=void 0;var Zl={randomUUID:typeof crypto<"u"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};Zt.default=Zl,Object.defineProperty(Jt,"__esModule",{value:!0}),Jt.default=void 0;var Er=Ja(Zt),Ql=Ja(Kt),eu=Ae;function Ja(e){return e&&e.__esModule?e:{default:e}}var tu=function(e,t,n){if(Er.default.randomUUID&&!t&&!e)return Er.default.randomUUID();const s=(e=e||{}).random||(e.rng||Ql.default)();if(s[6]=15&s[6]|64,s[8]=63&s[8]|128,t){n=n||0;for(let r=0;r<16;++r)t[n+r]=s[r];return t}return(0,eu.unsafeStringify)(s)};Jt.default=tu;var Qt={},en={};function nu(e,t,n,s){switch(e){case 0:return t&n^~t&s;case 1:case 3:return t^n^s;case 2:return t&n^t&s^n&s}}function xn(e,t){return e<<t|e>>>32-t}Object.defineProperty(en,"__esModule",{value:!0}),en.default=void 0;var su=function(e){const t=[1518500249,1859775393,2400959708,3395469782],n=[1732584193,4023233417,2562383102,271733878,3285377520];if(typeof e=="string"){const o=unescape(encodeURIComponent(e));e=[];for(let i=0;i<o.length;++i)e.push(o.charCodeAt(i))}else Array.isArray(e)||(e=Array.prototype.slice.call(e));e.push(128);const s=e.length/4+2,r=Math.ceil(s/16),a=new Array(r);for(let o=0;o<r;++o){const i=new Uint32Array(16);for(let l=0;l<16;++l)i[l]=e[64*o+4*l]<<24|e[64*o+4*l+1]<<16|e[64*o+4*l+2]<<8|e[64*o+4*l+3];a[o]=i}a[r-1][14]=8*(e.length-1)/Math.pow(2,32),a[r-1][14]=Math.floor(a[r-1][14]),a[r-1][15]=8*(e.length-1)&4294967295;for(let o=0;o<r;++o){const i=new Uint32Array(80);for(let p=0;p<16;++p)i[p]=a[o][p];for(let p=16;p<80;++p)i[p]=xn(i[p-3]^i[p-8]^i[p-14]^i[p-16],1);let l=n[0],u=n[1],d=n[2],m=n[3],h=n[4];for(let p=0;p<80;++p){const f=Math.floor(p/20),b=xn(l,5)+nu(f,u,d,m)+h+t[f]+i[p]>>>0;h=m,m=d,d=xn(u,30)>>>0,u=l,l=b}n[0]=n[0]+l>>>0,n[1]=n[1]+u>>>0,n[2]=n[2]+d>>>0,n[3]=n[3]+m>>>0,n[4]=n[4]+h>>>0}return[n[0]>>24&255,n[0]>>16&255,n[0]>>8&255,255&n[0],n[1]>>24&255,n[1]>>16&255,n[1]>>8&255,255&n[1],n[2]>>24&255,n[2]>>16&255,n[2]>>8&255,255&n[2],n[3]>>24&255,n[3]>>16&255,n[3]>>8&255,255&n[3],n[4]>>24&255,n[4]>>16&255,n[4]>>8&255,255&n[4]]};en.default=su,Object.defineProperty(Qt,"__esModule",{value:!0}),Qt.default=void 0;var ru=Za(Ce),au=Za(en);function Za(e){return e&&e.__esModule?e:{default:e}}var ou=(0,ru.default)("v5",80,au.default);Qt.default=ou;var tn={};Object.defineProperty(tn,"__esModule",{value:!0}),tn.default=void 0;tn.default="00000000-0000-0000-0000-000000000000";var nn={};Object.defineProperty(nn,"__esModule",{value:!0}),nn.default=void 0;var iu=function(e){return e&&e.__esModule?e:{default:e}}(qe),lu=function(e){if(!(0,iu.default)(e))throw TypeError("Invalid UUID");return parseInt(e.slice(14,15),16)};function Gn(e,t){if(!(e&&t&&e.length&&t.length))throw new Error("Bad alphabet");this.srcAlphabet=e,this.dstAlphabet=t}nn.default=lu,function(e){Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"NIL",{enumerable:!0,get:function(){return a.default}}),Object.defineProperty(e,"parse",{enumerable:!0,get:function(){return u.default}}),Object.defineProperty(e,"stringify",{enumerable:!0,get:function(){return l.default}}),Object.defineProperty(e,"v1",{enumerable:!0,get:function(){return t.default}}),Object.defineProperty(e,"v3",{enumerable:!0,get:function(){return n.default}}),Object.defineProperty(e,"v4",{enumerable:!0,get:function(){return s.default}}),Object.defineProperty(e,"v5",{enumerable:!0,get:function(){return r.default}}),Object.defineProperty(e,"validate",{enumerable:!0,get:function(){return i.default}}),Object.defineProperty(e,"version",{enumerable:!0,get:function(){return o.default}});var t=d(jt),n=d(zt),s=d(Jt),r=d(Qt),a=d(tn),o=d(nn),i=d(qe),l=d(Ae),u=d(ft);function d(m){return m&&m.__esModule?m:{default:m}}}(ja),Gn.prototype.convert=function(e){var t,n,s,r={},a=this.srcAlphabet.length,o=this.dstAlphabet.length,i=e.length,l=typeof e=="string"?"":[];if(!this.isValid(e))throw new Error('Number "'+e+'" contains of non-alphabetic digits ('+this.srcAlphabet+")");if(this.srcAlphabet===this.dstAlphabet)return e;for(t=0;t<i;t++)r[t]=this.srcAlphabet.indexOf(e[t]);do{for(n=0,s=0,t=0;t<i;t++)(n=n*a+r[t])>=o?(r[s++]=parseInt(n/o,10),n%=o):s>0&&(r[s++]=0);i=s,l=this.dstAlphabet.slice(n,n+1).concat(l)}while(s!==0);return l},Gn.prototype.isValid=function(e){for(var t=0;t<e.length;++t)if(this.srcAlphabet.indexOf(e[t])===-1)return!1;return!0};var uu=Gn;function at(e,t){var n=new uu(e,t);return function(s){return n.convert(s)}}at.BIN="01",at.OCT="01234567",at.DEC="0123456789",at.HEX="0123456789abcdef";var cu=at;const{v4:Cn,validate:du}=ja,kt=cu,kn={cookieBase90:"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ!#$%&'()*+-./:<=>?@[]^_`{|}~",flickrBase58:"123456789abcdefghijkmnopqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ",uuid25Base36:"0123456789abcdefghijklmnopqrstuvwxyz"},mu={consistentLength:!0};let Rn;const Tr=(e,t,n)=>{const s=t(e.toLowerCase().replace(/-/g,""));return n&&n.consistentLength?s.padStart(n.shortIdLength,n.paddingChar):s},Sr=(e,t)=>{const n=t(e).padStart(32,"0").match(/(\w{8})(\w{4})(\w{4})(\w{4})(\w{12})/);return[n[1],n[2],n[3],n[4],n[5]].join("-")};var hu=(()=>{const e=(t,n)=>{const s=t||kn.flickrBase58,r={...mu,...n};if([...new Set(Array.from(s))].length!==s.length)throw new Error("The provided Alphabet has duplicate characters resulting in unreliable results");const a=(o=s.length,Math.ceil(Math.log(2**128)/Math.log(o)));var o;const i={shortIdLength:a,consistentLength:r.consistentLength,paddingChar:s[0]},l=kt(kt.HEX,s),u=kt(s,kt.HEX),d=()=>Tr(Cn(),l,i),m={alphabet:s,fromUUID:h=>Tr(h,l,i),maxLength:a,generate:d,new:d,toUUID:h=>Sr(h,u),uuid:Cn,validate:(h,p=!1)=>{if(!h||typeof h!="string")return!1;const f=r.consistentLength?h.length===a:h.length<=a,b=h.split("").every(_=>s.includes(_));return p===!1?f&&b:f&&b&&du(Sr(h,u))}};return Object.freeze(m),m};return e.constants=kn,e.uuid=Cn,e.generate=()=>(Rn||(Rn=e(kn.flickrBase58).generate),Rn()),e})();const pu=bo(hu),Qa={[ae.NOT_STARTED]:"[ ]",[ae.IN_PROGRESS]:"[/]",[ae.COMPLETE]:"[x]",[ae.CANCELLED]:"[-]"},eo=pu(void 0,{consistentLength:!0});function gu(e,t){if(e.uuid===t)return e;if(e.subTasksData)for(const n of e.subTasksData){const s=gu(n,t);if(s)return s}}function to(e,t={}){const{shallow:n=!1,excludeUuid:s=!1,shortUuid:r=!0}=t;return no(e,{shallow:n,excludeUuid:s,shortUuid:r}).join(`
`)}function no(e,t={}){const{shallow:n=!1,excludeUuid:s=!1,shortUuid:r=!0}=t;let a="";s||(a=`UUID:${r?function(i){try{return eo.fromUUID(i)}catch{return i}}(e.uuid):e.uuid} `);const o=`${Qa[e.state]} ${a}NAME:${e.name} DESCRIPTION:${e.description}`;return n||!e.subTasksData||e.subTasksData.length===0?[o]:[o,...(e.subTasksData||[]).map(i=>no(i,t).map(l=>`-${l}`)).flat()]}function fu(e,t){var s;const n=(s=e.subTasksData)==null?void 0:s.map(r=>fu(r,t));return{...e,uuid:t!=null&&t.keepUuid?e.uuid:crypto.randomUUID(),subTasks:(n==null?void 0:n.map(r=>r.uuid))||[],subTasksData:n}}function td(e,t={}){if(!e.trim())throw new Error("Empty markdown");const n=e.split(`
`);let s=0;for(const u of n)if(u.trim()&&Ir(u)===0)try{Vn(u,t),s++}catch{}if(s===0)throw new Error("No root task found");if(s>1)throw new Error(`Multiple root tasks found (${s}). There can only be one root task per conversation. All other tasks must be subtasks (indented with dashes). Root task format: [ ] UUID:xxx NAME:yyy DESCRIPTION:zzz (no dashes). Subtask format: -[ ] UUID:xxx NAME:yyy DESCRIPTION:zzz (with dashes).`);const r=e.split(`
`);function a(){for(;r.length>0;){const u=r.shift(),d=Ir(u);try{return{task:Vn(u,t),level:d}}catch{}}}const o=a();if(!o)throw new Error("No root task found");const i=[o.task];let l;for(;l=a();){const u=i[l.level-1];if(!u)throw new Error(`Invalid markdown: level ${l.level+1} has no parent
Line: ${l.task.name} is missing a parent
Current tasks: 
${to(o.task)}`);u.subTasksData&&u.subTasks||(u.subTasks=[],u.subTasksData=[]),u.subTasksData.push(l.task),u.subTasks.push(l.task.uuid),i[l.level]=l.task,i.splice(l.level+1)}return o.task}function Ir(e){let t=0,n=0;for(;n<e.length&&(e[n]===" "||e[n]==="	");)e[n]===" "?t+=.5:e[n]==="	"&&(t+=1),n++;for(;n<e.length&&e[n]==="-";)t+=1,n++;return Math.floor(t)}function Vn(e,t={}){const{excludeUuid:n=!1,shortUuid:s=!0}=t;let r=0;for(;r<e.length&&(e[r]===" "||e[r]==="	"||e[r]==="-");)r++;const a=e.substring(r),o=a.match(/^\s*\[([ x\-/?])\]/);if(!o)throw new Error(`Invalid task line: ${e} (missing state)`);const i=o[1],l=Object.entries(Qa).reduce((p,[f,b])=>(p[b.substring(1,2)]=f,p),{})[i]||ae.NOT_STARTED,u=a.substring(o.index+o[0].length).trim();let d,m,h;if(n){const p=/(?:name|NAME):([^]*?)(?=(?:description|DESCRIPTION):)(?:description|DESCRIPTION):(.*)$/i,f=u.match(p);if(!f){const b=/\b(?:name|NAME):/i.test(u),_=/\b(?:description|DESCRIPTION):/i.test(u);throw!b||!_?new Error(`Invalid task line: ${e} (missing required fields)`):u.toLowerCase().indexOf("name:")<u.toLowerCase().indexOf("description:")?new Error(`Invalid task line: ${e} (invalid format)`):new Error(`Invalid task line: ${e} (incorrect field order)`)}if(m=f[1].trim(),h=f[2].trim(),!m)throw new Error(`Invalid task line: ${e} (missing required fields)`);d=crypto.randomUUID()}else{const p=/(?:uuid|UUID):([^]*?)(?=(?:name|NAME):)(?:name|NAME):([^]*?)(?=(?:description|DESCRIPTION):)(?:description|DESCRIPTION):(.*)$/i,f=u.match(p);if(!f){const b=/\b(?:uuid|UUID):/i.test(u),_=/\b(?:name|NAME):/i.test(u),E=/\b(?:description|DESCRIPTION):/i.test(u);if(!b||!_||!E)throw new Error(`Invalid task line: ${e} (missing required fields)`);const C=u.toLowerCase().indexOf("uuid:"),y=u.toLowerCase().indexOf("name:"),oe=u.toLowerCase().indexOf("description:");throw C<y&&y<oe?new Error(`Invalid task line: ${e} (invalid format)`):new Error(`Invalid task line: ${e} (incorrect field order)`)}if(d=f[1].trim(),m=f[2].trim(),h=f[3].trim(),!d||!m)throw new Error(`Invalid task line: ${e} (missing required fields)`);if(d==="NEW_UUID")d=crypto.randomUUID();else if(s)try{d=function(b){try{return eo.toUUID(b)}catch{return b}}(d)}catch{}}return{uuid:d,name:m,description:h,state:l,subTasks:[],lastUpdated:Date.now(),lastUpdatedBy:ps.USER}}const Ze=e=>({uuid:crypto.randomUUID(),name:"New Task",description:"New task description",state:ae.NOT_STARTED,subTasks:[],lastUpdated:Date.now(),lastUpdatedBy:ps.USER,...e}),Nr=Ze({name:"Task 1.1",description:"This is the first sub task",state:ae.IN_PROGRESS}),wr=Ze({name:"Task 1.2.1",description:"This is a nested sub task, child of Task 1.2",state:ae.NOT_STARTED}),xr=Ze({name:"Task 1.2.2",description:"This is another nested sub task, child of Task 1.2",state:ae.IN_PROGRESS}),Cr=Ze({name:"Task 1.2",description:"This is the second sub task",state:ae.COMPLETE,subTasks:[wr.uuid,xr.uuid],subTasksData:[wr,xr]}),kr=Ze({name:"Task 1.3",description:"This is the third sub task",state:ae.CANCELLED}),nd=to(Ze({name:"Task 1",description:"This is the first task",state:ae.NOT_STARTED,subTasks:[Nr.uuid,Cr.uuid,kr.uuid],subTasksData:[Nr,Cr,kr]}));function so(e){const t=e.split(`
`);let n=null;const s={created:[],updated:[],deleted:[]};for(const r of t){const a=r.trim();if(a!=="## Created Tasks")if(a!=="## Updated Tasks")if(a!=="## Deleted Tasks"){if(n&&(a.startsWith("[ ]")||a.startsWith("[/]")||a.startsWith("[x]")||a.startsWith("[-]")))try{const o=Vn(a,{excludeUuid:!1,shortUuid:!0});o&&s[n].push(o)}catch{}}else n="deleted";else n="updated";else n="created"}return s}function sd(e){const t=e.match(/Created: (\d+), Updated: (\d+), Deleted: (\d+)/);if(t)return{created:parseInt(t[1],10),updated:parseInt(t[2],10),deleted:parseInt(t[3],10)};const n=so(ro(e));return{created:n.created.length,updated:n.updated.length,deleted:n.deleted.length}}function ro(e){const t=e.indexOf("# Task Changes");if(t===-1)return"";const n=e.substring(t),s=[`
New and Updated Tasks:`,`
Remember:`,`

---`];let r=n.length;for(const i of s){const l=n.indexOf(i);l!==-1&&l<r&&(r=l)}const a=n.substring(0,r),o=a.indexOf(`
`);return o===-1?"":a.substring(o+1).trim()}function rd(e){return so(ro(e))}class yu{static getTaskOrchestratorPrompt(t){const{taskTree:n,surroundingContext:s}=t,r=this.buildTaskContext(n,s);return`Please utilize sub-agents to complete the following task tree.
Here are the details, along with a suggestion prompt.
You may use 1 or more sub-agents in to complete the below task.
For each sub-agent, please give it the relevant context and breakdown of the below task.

## Task Details
**Name:** ${n.name}
${n.description?`**Description:** ${n.description}`:""}
**Status:** ${n.state}

## Task Context
${r}

## Instructions
Please complete this task according to the requirements.
When you are done, report back on the completion status with a summary of changes made,
important context, and other relevant information for the supervisor.

Focus on this specific task tree while being aware of the broader context provided above.`}static getTaskMentionId(t){return`task:${t.taskUuid}:${t.taskTree.name.replace(/\s+/g,"_")}`}static getTaskMentionLabel(t){const{taskTree:n,surroundingContext:s}=t;return s.targetTaskPath.length>1?`${s.targetTaskPath.slice(0,-1).join(" → ")} → ${n.name}`:n.name}static buildTaskContext(t,n){const{rootTask:s,targetTaskPath:r}=n;let a=`This task is part of a larger project: "${s.name}"`;return s.description&&(a+=`

**Project Description:** ${s.description}`),r.length>1&&(a+=`

**Task Path:** ${r.join(" → ")}`),t.subTasksData&&t.subTasksData.length>0&&(a+=`

**Subtasks:**`,t.subTasksData.forEach((o,i)=>{a+=`
${i+1}. ${o.name} (${o.state})`,o.description&&(a+=` - ${o.description}`)})),a}}function nt(e){var t;return((t=e.extraData)==null?void 0:t.isAgentConversation)===!0}var _u=(e=>(e[e.active=0]="active",e[e.inactive=1]="inactive",e))(_u||{});class bu{constructor(){this._controllers=new Set,this._timeoutIds=new Set}addCallback(t,n){const s=new AbortController,r=setTimeout(()=>{t(s.signal),this._controllers.delete(s),this._timeoutIds.delete(r)},n);this._controllers.add(s),this._timeoutIds.add(r)}cancelAll(){this._controllers.forEach(t=>t.abort()),this._timeoutIds.forEach(t=>clearTimeout(t)),this._controllers.clear(),this._timeoutIds.clear()}}function An(e){return e.reduce((t,n)=>t+ao(n),0)}function ao(e){let t=0;return e.request_nodes?t+=JSON.stringify(e.request_nodes).length:t+=(e.request_message||"").length,e.response_nodes?t+=JSON.stringify(e.response_nodes).length:t+=(e.response_text||"").length,t}const he={triggerOnHistorySizeChars:0,historyTailSizeCharsToExclude:0,triggerOnHistorySizeCharsWhenCacheExpiring:0,prompt:"",cacheTTLMs:0,bufferTimeBeforeCacheExpirationMs:0,summaryNodeRequestMessageTemplate:`
<supervisor>
Conversation history between Agent(you) and the user and history of tool calls was summarized to reduce context size.
Summary was generated by Agent(you) so 'I' in the summary represents Agent(you).
Here is the summary:
<summary>
{summary}
</summary>
Continue the conversation and finish the task given by the user from this point.
</supervisor>`,summaryNodeResponseMessage:"Ok. I will continue the conversation from this point."};class vu{constructor(t,n,s){c(this,"historySummaryVersion",2);c(this,"_callbacksManager",new bu);c(this,"_params");this._conversationModel=t,this._extensionClient=n,this._chatFlagModel=s,this._params=Rr(s.historySummaryParams),s.subscribe(r=>{this._params=Rr(r.historySummaryParams)})}cancelRunningOrScheduledSummarizations(){this._callbacksManager.cancelAll()}clearStaleHistorySummaryNodes(t){return t.filter(n=>!We(n)||n.summaryVersion===this.historySummaryVersion)}maybeScheduleSummarization(t){if(!this._chatFlagModel.useHistorySummary||this._params.triggerOnHistorySizeCharsWhenCacheExpiring<=0)return;const n=this._params.cacheTTLMs-t-this._params.bufferTimeBeforeCacheExpirationMs;n>0&&this._callbacksManager.addCallback(s=>{this.maybeAddHistorySummaryNode(!0,s)},n)}preprocessChatHistory(t){const n=t.findLastIndex(s=>We(s)&&s.summaryVersion===this.historySummaryVersion);return this._chatFlagModel.useHistorySummary?(n>0&&(console.info(`Using history summary node found at index ${n} with requestId: ${t[n].request_id}`),t=t.slice(n)),t=t.filter(s=>!We(s)||s.summaryVersion===this.historySummaryVersion)):t=t.filter(s=>!We(s)),t}async maybeAddHistorySummaryNode(t=!1,n){var Ge,w,Qe;if(console.log("maybeAddHistorySummaryNode. isCacheAboutToExpire: ",t),!this._params.prompt||this._params.prompt.trim()==="")return console.log("maybeAddHistorySummaryNode. empty prompt"),!1;const s=this._conversationModel.convertHistoryToExchanges(this._conversationModel.chatHistory),r=t?this._params.triggerOnHistorySizeCharsWhenCacheExpiring:this._params.triggerOnHistorySizeChars;if(console.log("maybeAddHistorySummaryNode. maxCharsThreshold: ",r),r<=0)return!1;const{head:a,tail:o,headSizeChars:i,tailSizeChars:l}=function(G,Ve,on,io){if(G.length===0)return{head:[],tail:[],headSizeChars:0,tailSizeChars:0};const ln=[],et=[];let vt=0,ys=0,_s=0;for(let un=G.length-1;un>=0;un--){const cn=G[un],Et=ao(cn);vt+Et<Ve||et.length<io?(et.push(cn),_s+=Et):(ln.push(cn),ys+=Et),vt+=Et}return vt<on?(et.push(...ln),{head:[],tail:et.reverse(),headSizeChars:0,tailSizeChars:vt}):{head:ln.reverse(),tail:et.reverse(),headSizeChars:ys,tailSizeChars:_s}}(s,this._params.historyTailSizeCharsToExclude,r,1);if(console.log("maybeAddHistorySummaryNode. headSizeChars: ",i," tailSizeChars: ",l),a.length===0)return console.log("maybeAddHistorySummaryNode. head is empty. nothing to summarize"),!1;const u=An(s),d=An(a),m=An(o),h={totalHistoryCharCount:u,totalHistoryExchangeCount:s.length,headCharCount:d,headExchangeCount:a.length,headLastRequestId:((Ge=a.at(-1))==null?void 0:Ge.request_id)??"",tailCharCount:m,tailExchangeCount:o.length,tailLastRequestId:((w=o.at(-1))==null?void 0:w.request_id)??"",summaryCharCount:0,summarizationDurationMs:0,isCacheAboutToExpire:t,isAborted:!1};let p=((Qe=a.at(-1))==null?void 0:Qe.response_nodes)??[],f=p.filter(G=>G.type===R.TOOL_USE);f.length>0&&(a.at(-1).response_nodes=p.filter(G=>G.type!==R.TOOL_USE)),console.info("Summarizing %d turns of conversation history.",a.length);const b=Date.now(),{responseText:_,requestId:E}=await this._conversationModel.sendSilentExchange({request_message:this._params.prompt,disableRetrieval:!0,disableSelectedCodeDetails:!0,chatHistory:a}),C=Date.now();if(h.summaryCharCount=_.length,h.summarizationDurationMs=C-b,h.isAborted=!!(n!=null&&n.aborted),this._extensionClient.reportAgentRequestEvent({eventName:Bt.chatHistorySummarization,conversationId:this._conversationModel.conversationId,requestId:E??"UNKNOWN_REQUEST_ID",chatHistoryLength:this._conversationModel.chatHistory.length,eventData:{chatHistorySummarizationData:h}}),n==null?void 0:n.aborted)return console.log("maybeAddHistorySummaryNode. aborted"),!1;if(!E||_.trim()==="")return console.log("maybeAddHistorySummaryNode. no request id or empty response"),!1;const y=this._params.summaryNodeRequestMessageTemplate.replace("{summary}",_),oe=this._params.summaryNodeResponseMessage,Ne={chatItemType:Ke.historySummary,summaryVersion:this.historySummaryVersion,request_id:E,request_message:y,response_text:oe,structured_output_nodes:[{id:f.map(G=>G.id).reduce((G,Ve)=>Math.max(G,Ve),-1)+1,type:R.RAW_RESPONSE,content:oe},...f],status:I.success,seen_state:K.seen,timestamp:new Date().toISOString()},H=this._conversationModel.chatHistory.findLastIndex(G=>G.request_id===a.at(-1).request_id)+1;return console.info("Adding a history summary node at index %d",H),this._conversationModel.insertChatItem(H,Ne),!0}}function Rr(e){try{if(!e)return console.log("historySummaryParams is empty. Using default params"),he;const t=JSON.parse(e),n={triggerOnHistorySizeChars:t.trigger_on_history_size_chars||he.triggerOnHistorySizeChars,historyTailSizeCharsToExclude:t.history_tail_size_chars_to_exclude||he.historyTailSizeCharsToExclude,triggerOnHistorySizeCharsWhenCacheExpiring:t.trigger_on_history_size_chars_when_cache_expiring||he.triggerOnHistorySizeCharsWhenCacheExpiring,prompt:t.prompt||he.prompt,cacheTTLMs:t.cache_ttl_ms||he.cacheTTLMs,bufferTimeBeforeCacheExpirationMs:t.buffer_time_before_cache_expiration_ms||he.bufferTimeBeforeCacheExpirationMs,summaryNodeRequestMessageTemplate:t.summary_node_request_message_template||he.summaryNodeRequestMessageTemplate,summaryNodeResponseMessage:t.summary_node_response_message||he.summaryNodeResponseMessage};n.summaryNodeRequestMessageTemplate.includes("{summary}")||(console.error("summaryNodeRequestMessageTemplate must contain {summary}. Using default template"),n.summaryNodeRequestMessageTemplate=he.summaryNodeRequestMessageTemplate);const s={...n,prompt:n.prompt.slice(0,10)+"..."};return console.log("historySummaryParams updated: ",s),n}catch(t){return console.error("Failed to parse history_summary_params:",t),he}}const Eu=new Error("request for lock canceled");var Tu=function(e,t,n,s){return new(n||(n=Promise))(function(r,a){function o(u){try{l(s.next(u))}catch(d){a(d)}}function i(u){try{l(s.throw(u))}catch(d){a(d)}}function l(u){var d;u.done?r(u.value):(d=u.value,d instanceof n?d:new n(function(m){m(d)})).then(o,i)}l((s=s.apply(e,t||[])).next())})};class Su{constructor(t,n=Eu){this._value=t,this._cancelError=n,this._queue=[],this._weightedWaiters=[]}acquire(t=1,n=0){if(t<=0)throw new Error(`invalid weight ${t}: must be positive`);return new Promise((s,r)=>{const a={resolve:s,reject:r,weight:t,priority:n},o=Ar(this._queue,i=>n<=i.priority);o===-1&&t<=this._value?this._dispatchItem(a):this._queue.splice(o+1,0,a)})}runExclusive(t){return Tu(this,arguments,void 0,function*(n,s=1,r=0){const[a,o]=yield this.acquire(s,r);try{return yield n(a)}finally{o()}})}waitForUnlock(t=1,n=0){if(t<=0)throw new Error(`invalid weight ${t}: must be positive`);return this._couldLockImmediately(t,n)?Promise.resolve():new Promise(s=>{this._weightedWaiters[t-1]||(this._weightedWaiters[t-1]=[]),function(r,a){const o=Ar(r,i=>a.priority<=i.priority);r.splice(o+1,0,a)}(this._weightedWaiters[t-1],{resolve:s,priority:n})})}isLocked(){return this._value<=0}getValue(){return this._value}setValue(t){this._value=t,this._dispatchQueue()}release(t=1){if(t<=0)throw new Error(`invalid weight ${t}: must be positive`);this._value+=t,this._dispatchQueue()}cancel(){this._queue.forEach(t=>t.reject(this._cancelError)),this._queue=[]}_dispatchQueue(){for(this._drainUnlockWaiters();this._queue.length>0&&this._queue[0].weight<=this._value;)this._dispatchItem(this._queue.shift()),this._drainUnlockWaiters()}_dispatchItem(t){const n=this._value;this._value-=t.weight,t.resolve([n,this._newReleaser(t.weight)])}_newReleaser(t){let n=!1;return()=>{n||(n=!0,this.release(t))}}_drainUnlockWaiters(){if(this._queue.length===0)for(let t=this._value;t>0;t--){const n=this._weightedWaiters[t-1];n&&(n.forEach(s=>s.resolve()),this._weightedWaiters[t-1]=[])}else{const t=this._queue[0].priority;for(let n=this._value;n>0;n--){const s=this._weightedWaiters[n-1];if(!s)continue;const r=s.findIndex(a=>a.priority<=t);(r===-1?s:s.splice(0,r)).forEach(a=>a.resolve())}}}_couldLockImmediately(t,n){return(this._queue.length===0||this._queue[0].priority<n)&&t<=this._value}}function Ar(e,t){for(let n=e.length-1;n>=0;n--)if(t(e[n]))return n;return-1}var Iu=function(e,t,n,s){return new(n||(n=Promise))(function(r,a){function o(u){try{l(s.next(u))}catch(d){a(d)}}function i(u){try{l(s.throw(u))}catch(d){a(d)}}function l(u){var d;u.done?r(u.value):(d=u.value,d instanceof n?d:new n(function(m){m(d)})).then(o,i)}l((s=s.apply(e,t||[])).next())})};class Nu{constructor(t){this._semaphore=new Su(1,t)}acquire(){return Iu(this,arguments,void 0,function*(t=0){const[,n]=yield this._semaphore.acquire(1,t);return n})}runExclusive(t,n=0){return this._semaphore.runExclusive(()=>t(),1,n)}isLocked(){return this._semaphore.isLocked()}waitForUnlock(t=0){return this._semaphore.waitForUnlock(1,t)}release(){this._semaphore.isLocked()&&this._semaphore.release()}cancel(){return this._semaphore.cancel()}}const Pt="temp-fe";class ee{constructor(t,n,s,r){c(this,"_state");c(this,"_subscribers",new Set);c(this,"_focusModel",new Bi);c(this,"_onSendExchangeListeners",[]);c(this,"_onNewConversationListeners",[]);c(this,"_onHistoryDeleteListeners",[]);c(this,"_onBeforeChangeConversationListeners",[]);c(this,"_totalCharactersCacheThrottleMs",1e3);c(this,"_sendUserMessageMutex",new Nu);c(this,"_totalCharactersStore");c(this,"_chatHistorySummarizationModel");c(this,"subscribe",t=>(this._subscribers.add(t),t(this),()=>{this._subscribers.delete(t)}));c(this,"setConversation",(t,n=!0,s=!0)=>{const r=t.id!==this._state.id;r&&s&&(t.toolUseStates=Object.fromEntries(Object.entries(t.toolUseStates??{}).map(([o,i])=>{if(i.requestId&&i.toolUseId){const{requestId:l,toolUseId:u}=yr(o);return l===i.requestId&&u===i.toolUseId||console.warn("Tool use state key does not match request and tool use IDs. Got key ",o,"but object has ",Sn(i)),[o,i]}return[o,{...i,...yr(o)}]})),(t=this._notifyBeforeChangeConversation(this._state,t)).lastInteractedAtIso=new Date().toISOString()),n&&r&&this.isValid&&(this.saveDraftActiveContextIds(),this._unloadContextFromConversation(this._state));const a=ee.isEmpty(t);if(r&&a){const o=this._state.draftExchange;o&&(t.draftExchange=o)}return this._state=t,this._focusModel.setItems(this._state.chatHistory.filter($)),this._focusModel.initFocusIdx(-1),this._subscribers.forEach(o=>o(this)),this._saveConversation(this._state),r&&(this._loadContextFromConversation(t),this.loadDraftActiveContextIds(),this._onNewConversationListeners.forEach(o=>o())),!0});c(this,"update",t=>{this.setConversation({...this._state,...t}),this._totalCharactersStore.updateStore()});c(this,"toggleIsPinned",()=>{this.update({isPinned:!this.isPinned})});c(this,"setName",t=>{this.update({name:t})});c(this,"setSelectedModelId",t=>{this.update({selectedModelId:t})});c(this,"updateFeedback",(t,n)=>{this.update({feedbackStates:{...this._state.feedbackStates,[t]:n}})});c(this,"updateToolUseState",t=>{this.update({toolUseStates:{...this._state.toolUseStates,[Sn(t)]:t}})});c(this,"getToolUseState",(t,n)=>t===void 0||n===void 0||this.toolUseStates===void 0?{phase:x.unknown,requestId:t??"",toolUseId:n??""}:this.toolUseStates[Sn({requestId:t,toolUseId:n})]||{phase:x.new});c(this,"getLastToolUseId",()=>{var s,r;const t=this.lastExchange;if(!t)return;const n=(((s=t==null?void 0:t.structured_output_nodes)==null?void 0:s.filter(a=>a.type===R.TOOL_USE))??[]).at(-1);return n?(r=n.tool_use)==null?void 0:r.tool_use_id:void 0});c(this,"getLastToolUseState",()=>{var s;const t=this.lastExchange;if(!t)return{phase:x.unknown};const n=function(r=[]){let a;for(const o of r){if(o.type===R.TOOL_USE)return o;o.type===R.TOOL_USE_START&&(a=o)}return a}(t==null?void 0:t.structured_output_nodes);return n?this.getToolUseState(t.request_id,(s=n.tool_use)==null?void 0:s.tool_use_id):{phase:x.unknown}});c(this,"addExchange",(t,n)=>{const s=this._state.chatHistory;let r,a;r=n===void 0?[...s,t]:n===-1?s.length===0?[t]:[...s.slice(0,-1),t,s[s.length-1]]:[...s.slice(0,n),t,...s.slice(n)],$(t)&&(a=t.request_id?{...this._state.feedbackStates,[t.request_id]:{selectedRating:Ba.unset,feedbackNote:""}}:void 0),this.update({chatHistory:r,...a?{feedbackStates:a}:{},lastUrl:void 0})});c(this,"addExchangeBeforeLast",t=>{this.addExchange(t,-1)});c(this,"resetShareUrl",()=>{this.update({lastUrl:void 0})});c(this,"updateExchangeById",(t,n,s=!1)=>{var i;const r=this.exchangeWithRequestId(n);if(r===null)return console.warn("No exchange with this request ID found."),!1;s&&t.response_text!==void 0&&(t.response_text=(r.response_text??"")+(t.response_text??"")),s&&(t.structured_output_nodes=function(l=[]){const u=Rl(l);return u&&u.type===R.TOOL_USE?l.filter(d=>d.type!==R.TOOL_USE_START):l}([...r.structured_output_nodes??[],...t.structured_output_nodes??[]])),t.stop_reason!==r.stop_reason&&r.stop_reason&&t.stop_reason===go.REASON_UNSPECIFIED&&(t.stop_reason=r.stop_reason),s&&t.workspace_file_chunks!==void 0&&(t.workspace_file_chunks=[...r.workspace_file_chunks??[],...t.workspace_file_chunks??[]]);const a=(i=(t.structured_output_nodes||[]).find(l=>l.type===R.MAIN_TEXT_FINISHED))==null?void 0:i.content;a&&a!==t.response_text&&(t.response_text=a);let o=this._state.isShareable||On({...r,...t});return this.update({chatHistory:this.chatHistory.map(l=>l.request_id===n?{...l,...t}:l),isShareable:o}),!0});c(this,"clearMessagesFromHistory",t=>{const n=this._collectToolUseIdsFromMessages(this.chatHistory.filter(s=>s.request_id&&t.has(s.request_id)));this.update({chatHistory:this.chatHistory.filter(s=>!s.request_id||!t.has(s.request_id))}),this._extensionClient.clearMetadataFor({requestIds:Array.from(t),toolUseIds:n})});c(this,"clearHistory",()=>{const t=this._collectToolUseIdsFromMessages(this.chatHistory);this._extensionClient.clearMetadataFor({requestIds:this.requestIds,toolUseIds:t}),this.update({chatHistory:[]})});c(this,"clearHistoryFrom",async(t,n=!0)=>{const s=this.historyFrom(t,n),r=s.map(o=>o.request_id).filter(o=>o!==void 0),a=this._collectToolUseIdsFromMessages(s);this.update({chatHistory:this.historyTo(t,!n)}),this._extensionClient.clearMetadataFor({requestIds:r,toolUseIds:a}),s.forEach(o=>{this._onHistoryDeleteListeners.forEach(i=>i(o))})});c(this,"clearMessageFromHistory",t=>{const n=this.chatHistory.find(r=>r.request_id===t),s=n?this._collectToolUseIdsFromMessages([n]):[];this.update({chatHistory:this.chatHistory.filter(r=>r.request_id!==t)}),this._extensionClient.clearMetadataFor({requestIds:[t],toolUseIds:s})});c(this,"_collectToolUseIdsFromMessages",t=>{var s;const n=[];for(const r of t)if($(r)&&r.structured_output_nodes)for(const a of r.structured_output_nodes)a.type===R.TOOL_USE&&((s=a.tool_use)!=null&&s.tool_use_id)&&n.push(a.tool_use.tool_use_id);return n});c(this,"historyTo",(t,n=!1)=>{const s=this.chatHistory.findIndex(r=>r.request_id===t);return s===-1?[]:this.chatHistory.slice(0,n?s+1:s)});c(this,"historyFrom",(t,n=!0)=>{const s=this.chatHistory.findIndex(r=>r.request_id===t);return s===-1?[]:this.chatHistory.slice(n?s:s+1)});c(this,"resendLastExchange",async()=>{const t=this.lastExchange;if(t&&!this.awaitingReply)return this.resendTurn(t)});c(this,"resendTurn",t=>this.awaitingReply?Promise.resolve():(this._removeTurn(t),this.sendExchange({chatItemType:t.chatItemType,request_message:t.request_message,rich_text_json_repr:t.rich_text_json_repr,status:I.draft,mentioned_items:t.mentioned_items,structured_request_nodes:t.structured_request_nodes,disableSelectedCodeDetails:t.disableSelectedCodeDetails,chatHistory:t.chatHistory,model_id:t.model_id},!1,t.request_id)));c(this,"_removeTurn",t=>{this.update({chatHistory:this.chatHistory.filter(n=>n!==t&&(!t.request_id||n.request_id!==t.request_id))})});c(this,"exchangeWithRequestId",t=>this.chatHistory.find(n=>n.request_id===t)||null);c(this,"resetTotalCharactersCache",()=>{this._totalCharactersStore.resetCache()});c(this,"markSeen",async t=>{if(!t.request_id||!this.chatHistory.find(s=>s.request_id===t.request_id))return;const n={seen_state:K.seen};this.update({chatHistory:this.chatHistory.map(s=>s.request_id===t.request_id?{...s,...n}:s)})});c(this,"createStructuredRequestNodes",t=>this._jsonToStructuredRequest(t));c(this,"saveDraftMentions",t=>{if(!this.draftExchange)return;const n=t.filter(s=>!s.personality&&!s.task);this.update({draftExchange:{...this.draftExchange,mentioned_items:n}})});c(this,"saveDraftActiveContextIds",()=>{const t=this._specialContextInputModel.recentActiveItems.map(n=>n.id);this.update({draftActiveContextIds:t})});c(this,"loadDraftActiveContextIds",()=>{const t=new Set(this.draftActiveContextIds??[]),n=this._specialContextInputModel.recentItems.filter(r=>t.has(r.id)||r.recentFile||r.selection||r.sourceFolder),s=this._specialContextInputModel.recentItems.filter(r=>!(t.has(r.id)||r.recentFile||r.selection||r.sourceFolder));this._specialContextInputModel.markItemsActive(n.reverse()),this._specialContextInputModel.markItemsInactive(s.reverse())});c(this,"saveDraftExchange",(t,n)=>{var o,i,l;const s=t!==((o=this.draftExchange)==null?void 0:o.request_message),r=n!==((i=this.draftExchange)==null?void 0:i.rich_text_json_repr);if(!s&&!r)return;const a=(l=this.draftExchange)==null?void 0:l.mentioned_items;this.update({draftExchange:{request_message:t,rich_text_json_repr:n,mentioned_items:a,status:I.draft}})});c(this,"clearDraftExchange",()=>{const t=this.draftExchange;return this.update({draftExchange:void 0}),t});c(this,"sendDraftExchange",()=>{if(this._extensionClient.triggerUsedChatMetric(),!this.canSendDraft||!this.draftExchange)return!1;const t=this.clearDraftExchange();if(!t)return!1;const n=this._chatFlagModel.enableChatMultimodal&&t.rich_text_json_repr?this._jsonToStructuredRequest(t.rich_text_json_repr):void 0;return this.sendExchange({...t,structured_request_nodes:n,model_id:this.selectedModelId??void 0}).then(()=>{var s;if(!nt(this)){const r=!this.name&&this.chatHistory.length===1&&((s=this.firstExchange)==null?void 0:s.request_id)===this.chatHistory[0].request_id;this._chatFlagModel.summaryTitles&&r&&this.updateConversationTitle()}}).finally(()=>{var s;nt(this)&&this._extensionClient.reportAgentRequestEvent({eventName:Bt.sentUserMessage,conversationId:this.id,requestId:((s=this.lastExchange)==null?void 0:s.request_id)??"UNKNOWN_REQUEST_ID",chatHistoryLength:this.chatHistory.length})}),this.focusModel.setFocusIdx(void 0),!0});c(this,"cancelMessage",async()=>{var t;this.canCancelMessage&&((t=this.lastExchange)!=null&&t.request_id)&&(this.updateExchangeById({status:I.cancelled},this.lastExchange.request_id),await this._extensionClient.cancelChatStream(this.lastExchange.request_id))});c(this,"sendInstructionExchange",async(t,n)=>{let s=`${Pt}-${crypto.randomUUID()}`;const r={status:I.sent,request_id:s,request_message:t,model_id:this.selectedModelId??void 0,structured_output_nodes:[],seen_state:K.unseen,timestamp:new Date().toISOString()};this.addExchange(r);for await(const a of this._extensionClient.sendInstructionMessage(r,n)){if(!this.updateExchangeById(a,s,!0))return;s=a.request_id||s}});c(this,"updateConversationTitle",async()=>{const{responseText:t}=await this.sendSummaryExchange();this.update({name:t})});c(this,"checkAndGenerateAgentTitle",()=>{var n;if(!(!nt(this)||!this._chatFlagModel.summaryTitles||this.name)){var t;!this.name&&(t=this.chatHistory,t.filter(s=>Hn(s))).length===1&&!((n=this.extraData)!=null&&n.hasTitleGenerated)&&(this.update({extraData:{...this.extraData,hasTitleGenerated:!0}}),this.updateConversationTitle())}});c(this,"sendSummaryExchange",()=>{const t={status:I.sent,request_message:"Please provide a clear and concise summary of our conversation so far. The summary must be less than 6 words long. The summary must contain the key points of the conversation. The summary must be in the form of a title which will represent the conversation. The response should not include any additional formatting such as wrapping the response with quotation marks.",model_id:this.selectedModelId??void 0,chatItemType:Ke.summaryTitle,disableRetrieval:!0,disableSelectedCodeDetails:!0};return this.sendSilentExchange(t)});c(this,"generateCommitMessage",async()=>{let t=`${Pt}-${crypto.randomUUID()}`;const n={status:I.sent,request_id:t,request_message:"Please generate a commit message based on the diff of my staged and unstaged changes.",model_id:this.selectedModelId??void 0,mentioned_items:[],seen_state:K.unseen,chatItemType:Ke.generateCommitMessage,disableSelectedCodeDetails:!0,chatHistory:[],timestamp:new Date().toISOString()};this.addExchange(n);for await(const s of this._extensionClient.generateCommitMessage()){if(!this.updateExchangeById(s,t,!0))return;t=s.request_id||t}});c(this,"sendExchange",async(t,n=!1,s)=>{var d;this._chatHistorySummarizationModel.cancelRunningOrScheduledSummarizations(),this.updateLastInteraction();let r=`${Pt}-${crypto.randomUUID()}`,a=this._chatFlagModel.isModelIdValid(t.model_id)?t.model_id:void 0;if(ee.isNew(this._state)){const m=crypto.randomUUID(),h=this._state.id;try{await this._extensionClient.migrateConversationId(h,m)}catch(p){console.error("Failed to migrate conversation checkpoints:",p)}this._state={...this._state,id:m},this._saveConversation(this._state,!0),this._extensionClient.setCurrentConversation(m),this._subscribers.forEach(p=>p(this))}t=Mr(t);let o={status:I.sent,request_id:r,request_message:t.request_message,rich_text_json_repr:t.rich_text_json_repr,model_id:a,mentioned_items:t.mentioned_items,structured_output_nodes:t.structured_output_nodes,seen_state:K.unseen,chatItemType:t.chatItemType,disableSelectedCodeDetails:t.disableSelectedCodeDetails,chatHistory:t.chatHistory,structured_request_nodes:t.structured_request_nodes,timestamp:new Date().toISOString()};this.addExchange(o),this._loadContextFromExchange(o),this._onSendExchangeListeners.forEach(m=>m(o)),this._chatFlagModel.useHistorySummary&&!t.request_message&&await this._chatHistorySummarizationModel.maybeAddHistorySummaryNode()&&this.update({chatHistory:this._chatHistorySummarizationModel.clearStaleHistorySummaryNodes(this.chatHistory)}),o=await this._addIdeStateNode(o),this.updateExchangeById({structured_request_nodes:o.structured_request_nodes},r,!1);const i=Date.now();let l=!1;for await(const m of this.sendUserMessage(r,o,n,s)){if(((d=this.exchangeWithRequestId(r))==null?void 0:d.status)!==I.sent||!this.updateExchangeById(m,r,!0))return;if(r=m.request_id||r,!l&&nt(this)){const h=Date.now(),p=h-i;this._extensionClient.reportAgentRequestEvent({eventName:Bt.firstTokenReceived,conversationId:this.id,requestId:r,chatHistoryLength:this.chatHistory.length,eventData:{firstTokenTimingData:{userMessageSentTimestampMs:i,firstTokenReceivedTimestampMs:h,timeToFirstTokenMs:p}}}),l=!0}}const u=Date.now()-i;this._chatHistorySummarizationModel.maybeScheduleSummarization(u)});c(this,"sendSuggestedQuestion",t=>{this.sendExchange({request_message:t,status:I.draft}),this._extensionClient.triggerUsedChatMetric(),this._extensionClient.reportWebviewClientEvent(qa.chatUseSuggestedQuestion)});c(this,"recoverAllExchanges",async()=>{await Promise.all(this.recoverableExchanges.map(this.recoverExchange))});c(this,"recoverExchange",async t=>{var r;if(!t.request_id||t.status!==I.sent)return;let n=t.request_id;const s=(r=t.structured_output_nodes)==null?void 0:r.filter(a=>a.type===R.AGENT_MEMORY);this.updateExchangeById({...t,response_text:t.lastChunkId?t.response_text:"",structured_output_nodes:t.lastChunkId?t.structured_output_nodes??[]:s},n);for await(const a of this.getChatStream(t)){if(!this.updateExchangeById(a,n,!0))return;n=a.request_id||n}});c(this,"_loadContextFromConversation",t=>{t.chatHistory.forEach(n=>{$(n)&&this._loadContextFromExchange(n)})});c(this,"_loadContextFromExchange",t=>{t.mentioned_items&&(this._specialContextInputModel.updateItems(t.mentioned_items,[]),this._specialContextInputModel.markItemsActive(t.mentioned_items))});c(this,"_unloadContextFromConversation",t=>{t.chatHistory.forEach(n=>{$(n)&&this._unloadContextFromExchange(n)})});c(this,"_unloadContextFromExchange",t=>{t.mentioned_items&&this._specialContextInputModel.updateItems([],t.mentioned_items)});c(this,"updateLastInteraction",()=>{this.update({lastInteractedAtIso:new Date().toISOString()})});c(this,"_jsonToStructuredRequest",t=>{const n=[],s=a=>{var i;const o=n.at(-1);if((o==null?void 0:o.type)===L.TEXT){const l=((i=o.text_node)==null?void 0:i.content)??"",u={...o,text_node:{content:l+a}};n[n.length-1]=u}else n.push({id:n.length,type:L.TEXT,text_node:{content:a}})},r=a=>{var o,i,l,u,d;if(a.type==="doc"||a.type==="paragraph")for(const m of a.content??[])r(m);else if(a.type==="hardBreak")s(`
`);else if(a.type==="text")s(a.text??"");else if(a.type==="file"){if(typeof((o=a.attrs)==null?void 0:o.src)!="string")return void console.error("File source is not a string: ",(i=a.attrs)==null?void 0:i.src);if(a.attrs.isLoading)return;const m=(l=a.attrs)==null?void 0:l.title,h=yo(m);_o(m)?n.push({id:n.length,type:L.IMAGE_ID,image_id_node:{image_id:a.attrs.src,format:h}}):n.push({id:n.length,type:L.FILE_ID,file_id_node:{file_id:a.attrs.src,file_name:m}})}else if(a.type==="mention"){const m=(u=a.attrs)==null?void 0:u.data;m&&Ya(m)?n.push({id:n.length,type:L.TEXT,text_node:{content:Dl(this._chatFlagModel,m.personality.type)}}):m&&El(m)?n.push({id:n.length,type:L.TEXT,text_node:{content:yu.getTaskOrchestratorPrompt(m.task)}}):s(`@\`${(m==null?void 0:m.name)??(m==null?void 0:m.id)}\``)}else if(a.type==="askMode"){const m=(d=a.attrs)==null?void 0:d.prompt;m&&n.push({id:n.length,type:L.TEXT,text_node:{content:m}})}};return r(t),n});this._extensionClient=t,this._chatFlagModel=n,this._specialContextInputModel=s,this._saveConversation=r,this._state={...ee.create()},this._totalCharactersStore=this._createTotalCharactersStore(),this._chatHistorySummarizationModel=new vu(this,t,n)}get conversationId(){return this._state.id}insertChatItem(t,n){const s=[...this._state.chatHistory];s.splice(t,0,n),this.update({chatHistory:s})}_createTotalCharactersStore(){return cl(()=>{let t=0;const n=this._state.chatHistory;return this.convertHistoryToExchanges(n).forEach(s=>{t+=JSON.stringify(s).length}),this._state.draftExchange&&(t+=JSON.stringify(this._state.draftExchange).length),t},0,this._totalCharactersCacheThrottleMs)}async decidePersonaType(){var t;try{return(((t=(await this._extensionClient.getWorkspaceInfo()).trackedFileCount)==null?void 0:t.reduce((s,r)=>s+r,0))||0)<=4?q.PROTOTYPER:q.DEFAULT}catch(n){return console.error("Error determining persona type:",n),q.DEFAULT}}static create(t={}){const n=new Date().toISOString();return{id:t.id||crypto.randomUUID(),name:void 0,createdAtIso:n,lastInteractedAtIso:n,chatHistory:[],feedbackStates:{},toolUseStates:{},draftExchange:void 0,draftActiveContextIds:void 0,selectedModelId:void 0,requestIds:[],isPinned:!1,lastUrl:void 0,isShareable:!1,extraData:{},personaType:q.DEFAULT,...t}}static toSentenceCase(t){return t.charAt(0).toUpperCase()+t.slice(1)}static getDisplayName(t){if(t.name)return t.name;const n=t.chatHistory.find($);return n&&n.request_message?ee.toSentenceCase(n.request_message):nt(t)?"New Agent":"New Chat"}static isNew(t){return t.id===Ol}static isEmpty(t){var r;const n=t.chatHistory.filter(a=>$(a)),s=t.chatHistory.filter(a=>xu(a));return n.length===0&&s.length===0&&!((r=t.draftExchange)!=null&&r.request_message)}static isNamed(t){return t.name!==void 0&&t.name!==""}static getTime(t,n){return n==="lastMessageTimestamp"?ee.lastMessageTimestamp(t):n==="lastInteractedAt"?ee.lastInteractedAt(t):ee.createdAt(t)}static createdAt(t){return new Date(t.createdAtIso)}static lastInteractedAt(t){return new Date(t.lastInteractedAtIso)}static lastMessageTimestamp(t){var s;const n=(s=t.chatHistory.findLast($))==null?void 0:s.timestamp;return n?new Date(n):this.createdAt(t)}static isValid(t){return t.id!==void 0&&(!ee.isEmpty(t)||ee.isNamed(t))}onBeforeChangeConversation(t){return this._onBeforeChangeConversationListeners.push(t),()=>{this._onBeforeChangeConversationListeners=this._onBeforeChangeConversationListeners.filter(n=>n!==t)}}_notifyBeforeChangeConversation(t,n){let s=n;for(const r of this._onBeforeChangeConversationListeners){const a=r(t,s);a!==void 0&&(s=a)}return s}get extraData(){return this._state.extraData}set extraData(t){this.update({extraData:t})}get focusModel(){return this._focusModel}get isValid(){return ee.isValid(this._state)}get id(){return this._state.id}get name(){return this._state.name}get personaType(){return this._state.personaType??q.DEFAULT}get rootTaskUuid(){return this._state.rootTaskUuid}set rootTaskUuid(t){this.update({rootTaskUuid:t})}get displayName(){return ee.getDisplayName(this._state)}get createdAtIso(){return this._state.createdAtIso}get createdAt(){return ee.createdAt(this._state)}get chatHistory(){return this._state.chatHistory}get feedbackStates(){return this._state.feedbackStates}get toolUseStates(){return this._state.toolUseStates}get draftExchange(){return this._state.draftExchange}get selectedModelId(){return this._state.selectedModelId}get isPinned(){return!!this._state.isPinned}get extensionClient(){return this._extensionClient}get flags(){return this._chatFlagModel}addChatItem(t){this.addExchange(t)}get requestIds(){return this._state.chatHistory.map(t=>t.request_id).filter(t=>t!==void 0)}get hasDraft(){var s;const t=(((s=this.draftExchange)==null?void 0:s.request_message)??"").trim()!=="",n=this.hasImagesInDraft();return t||n}hasImagesInDraft(){var s;const t=(s=this.draftExchange)==null?void 0:s.rich_text_json_repr;if(!t)return!1;const n=r=>Array.isArray(r)?r.some(n):!!r&&(r.type==="file"||!(!r.content||!Array.isArray(r.content))&&r.content.some(n));return n(t)}get canSendDraft(){return this.hasDraft&&!this.awaitingReply}get canCancelMessage(){return this.awaitingReply}get firstExchange(){return this.chatHistory.find($)??null}get lastExchange(){return this.chatHistory.findLast($)??null}get canClearHistory(){return this._state.chatHistory.length!==0&&!this.awaitingReply}get recoverableExchanges(){return this._state.chatHistory.filter(t=>$(t)&&t.status===I.sent)}get successfulMessages(){return this._state.chatHistory.filter(t=>On(t)||lt(t)||We(t))}get totalCharactersStore(){return this._totalCharactersStore}convertHistoryToExchanges(t){if(t.length===0)return[];t=this._chatHistorySummarizationModel.preprocessChatHistory(t);const n=[];for(const s of t)if(On(s))n.push(Or(s));else if(We(s))n.push(Or(s));else if(lt(s)&&s.fromTimestamp!==void 0&&s.toTimestamp!==void 0&&s.revertTarget){const r=wu(s,1),a={request_message:"",response_text:"",request_id:s.request_id||crypto.randomUUID(),request_nodes:[r],response_nodes:[]};n.push(a)}return n}get awaitingReply(){return this.lastExchange!==null&&this.lastExchange.status===I.sent}get lastInteractedAtIso(){return this._state.lastInteractedAtIso}get draftActiveContextIds(){return this._state.draftActiveContextIds}async sendSilentExchange(t){const n=crypto.randomUUID();let s,r="";const a=await this._addIdeStateNode(Mr({...t,request_id:n,status:I.sent,timestamp:new Date().toISOString()}));for await(const o of this.sendUserMessage(n,a,!0))o.response_text&&(r+=o.response_text),o.request_id&&(s=o.request_id);return{responseText:r,requestId:s}}async*getChatStream(t){t.request_id&&(yield*this._extensionClient.getExistingChatStream(t.request_id,t.lastChunkId,{flags:this._chatFlagModel}))}_createStreamStateHandlers(t,n,s){return[]}_resolveUnresolvedToolUses(t,n,s){var d,m,h;if(t.length===0)return[t,n];const r=t[t.length-1],a=((d=r.response_nodes)==null?void 0:d.filter(p=>p.type===R.TOOL_USE))??[];if(a.length===0)return[t,n];const o=new Set;(m=n.structured_request_nodes)==null||m.forEach(p=>{var f;p.type===L.TOOL_RESULT&&((f=p.tool_result_node)!=null&&f.tool_use_id)&&o.add(p.tool_result_node.tool_use_id)});const i=a.filter(p=>{var b;const f=(b=p.tool_use)==null?void 0:b.tool_use_id;return f&&!o.has(f)});if(i.length===0)return[t,n];const l=i.map((p,f)=>{const b=p.tool_use.tool_use_id;return function(_,E,C,y){const oe=kl(E,_,y);let Ne;if(oe!==void 0)Ne=oe;else{let H;switch(E.phase){case x.runnable:H="Tool was cancelled before running.";break;case x.new:H="Cancelled by user.";break;case x.checkingSafety:H="Tool was cancelled during safety check.";break;case x.running:H="Tool was cancelled while running.";break;case x.cancelling:H="Tool cancellation was interrupted.";break;case x.cancelled:H="Cancelled by user.";break;case x.error:H="Tool execution failed.";break;case x.completed:H="Tool completed but result was unavailable.";break;case x.unknown:default:H="Cancelled by user.",E.phase!==x.unknown&&console.error(`Unexpected tool state phase: ${E.phase}`)}Ne={tool_use_id:_,content:H,is_error:!0}}return{id:C,type:L.TOOL_RESULT,tool_result_node:Ne}}(b,this.getToolUseState(r.request_id,b),Bn(n.structured_request_nodes??[])+f+1,this._chatFlagModel.enableDebugFeatures)});if((h=n.structured_request_nodes)==null?void 0:h.some(p=>p.type===L.TOOL_RESULT))return[t,{...n,structured_request_nodes:[...n.structured_request_nodes??[],...l]}];{const p={request_message:"",response_text:"OK.",request_id:crypto.randomUUID(),structured_request_nodes:l,structured_output_nodes:[],status:I.success,hidden:!0};return s||this.addExchangeBeforeLast(p),[t.concat(this.convertHistoryToExchanges([p])),n]}}async*sendUserMessage(t,n,s,r){const a=await this._sendUserMessageMutex.acquire();try{yield*this._sendUserMessage(t,n,s,r)}finally{a()}}async*_sendUserMessage(t,n,s,r){var m;const a=this._specialContextInputModel.chatActiveContext;let o;if(n.chatHistory!==void 0)o=n.chatHistory;else{let h=this.successfulMessages;if(n.chatItemType===Ke.summaryTitle){const p=h.findIndex(f=>f.chatItemType!==Ke.agentOnboarding&&Hn(f));p!==-1&&(h=h.slice(p))}o=this.convertHistoryToExchanges(h)}this._chatFlagModel.enableParallelTools&&([o,n]=this._resolveUnresolvedToolUses(o,n,s));let i=this.personaType;if(n.structured_request_nodes){const h=n.structured_request_nodes.find(p=>p.type===L.CHANGE_PERSONALITY);h&&h.change_personality_node&&(i=h.change_personality_node.personality_type)}const l={text:n.request_message,chatHistory:o,silent:s,modelId:n.model_id,context:a,userSpecifiedFiles:a.userSpecifiedFiles,externalSourceIds:(m=a.externalSources)==null?void 0:m.map(h=>h.id),disableRetrieval:n.disableRetrieval??!1,disableSelectedCodeDetails:n.disableSelectedCodeDetails??!1,nodes:n.structured_request_nodes,memoriesInfo:n.memoriesInfo,personaType:i,conversationId:this.id,createdTimestamp:Date.now(),requestIdOverride:r},u=this._createStreamStateHandlers(t,l,{flags:this._chatFlagModel}),d=this._extensionClient.startChatStreamWithRetry(t,l,{flags:this._chatFlagModel});for await(const h of d){let p=h;t=h.request_id||t;for(const f of u)p=f.handleChunk(p)??p;yield p}for(const h of u)yield*h.handleComplete();this.updateExchangeById({structured_request_nodes:n.structured_request_nodes},t)}onSendExchange(t){return this._onSendExchangeListeners.push(t),()=>{this._onSendExchangeListeners=this._onSendExchangeListeners.filter(n=>n!==t)}}onNewConversation(t){return this._onNewConversationListeners.push(t),()=>{this._onNewConversationListeners=this._onNewConversationListeners.filter(n=>n!==t)}}onHistoryDelete(t){return this._onHistoryDeleteListeners.push(t),()=>{this._onHistoryDeleteListeners=this._onHistoryDeleteListeners.filter(n=>n!==t)}}updateChatItem(t,n){return this.chatHistory.find(s=>s.request_id===t)===null?(console.warn("No exchange with this request ID found."),!1):(this.update({chatHistory:this.chatHistory.map(s=>s.request_id===t?{...s,...n}:s)}),!0)}async _addIdeStateNode(t){let n,s=(t.structured_request_nodes??[]).filter(r=>r.type!==L.IDE_STATE);try{n=await this._extensionClient.getChatRequestIdeState()}catch(r){console.error("Failed to add IDE state to exchange:",r)}return n?(s=[...s,{id:Bn(s)+1,type:L.IDE_STATE,ide_state_node:n}],{...t,structured_request_nodes:s}):t}}function wu(e,t){const n=(lt(e),e.fromTimestamp),s=(lt(e),e.toTimestamp),r=lt(e)&&e.revertTarget!==void 0;return{id:t,type:L.CHECKPOINT_REF,checkpoint_ref_node:{request_id:e.request_id||"",from_timestamp:n,to_timestamp:s,source:r?po.CHECKPOINT_REVERT:void 0}}}function Or(e){const t=(e.structured_output_nodes??[]).filter(n=>n.type===R.RAW_RESPONSE||n.type===R.TOOL_USE||n.type===R.TOOL_USE_START).map(n=>n.type===R.TOOL_USE_START?{...n,tool_use:{...n.tool_use,input_json:"{}"},type:R.TOOL_USE}:n);return{request_message:e.request_message,response_text:e.response_text??"",request_id:e.request_id||"",request_nodes:e.structured_request_nodes??[],response_nodes:t}}function Bn(e){return e.length>0?Math.max(...e.map(t=>t.id)):0}function Mr(e){var t;if(e.request_message.length>0&&!((t=e.structured_request_nodes)!=null&&t.some(n=>n.type===L.TEXT))){let n=e.structured_request_nodes??[];return n=[...n,{id:Bn(n)+1,type:L.TEXT,text_node:{content:e.request_message}}],{...e,structured_request_nodes:n}}return e}const ad="augment-welcome";var I=(e=>(e.draft="draft",e.sent="sent",e.failed="failed",e.success="success",e.cancelled="cancelled",e))(I||{}),$e=(e=>(e.running="running",e.awaitingUserAction="awaiting-user-action",e.notRunning="not-running",e))($e||{}),K=(e=>(e.seen="seen",e.unseen="unseen",e))(K||{}),Ke=(e=>(e.signInWelcome="sign-in-welcome",e.generateCommitMessage="generate-commit-message",e.summaryResponse="summary-response",e.summaryTitle="summary-title",e.educateFeatures="educate-features",e.agentOnboarding="agent-onboarding",e.agenticTurnDelimiter="agentic-turn-delimiter",e.agenticRevertDelimiter="agentic-revert-delimiter",e.agenticCheckpointDelimiter="agentic-checkpoint-delimiter",e.exchange="exchange",e.exchangePointer="exchange-pointer",e.historySummary="history-summary",e))(Ke||{});function Dr(e){return $(e)||Cu(e)||ku(e)}function $(e){return!!e&&(e.chatItemType===void 0||e.chatItemType==="agent-onboarding")}function On(e){return $(e)&&e.status==="success"}function xu(e){return!!e&&e.chatItemType==="exchange-pointer"}function od(e){return e.chatItemType==="sign-in-welcome"}function Cu(e){return e.chatItemType==="generate-commit-message"}function id(e){return e.chatItemType==="summary-response"}function ld(e){return e.chatItemType==="educate-features"}function ku(e){return e.chatItemType==="agent-onboarding"}function ud(e){return e.chatItemType==="agentic-turn-delimiter"}function lt(e){return e.chatItemType==="agentic-checkpoint-delimiter"}function We(e){return e.chatItemType==="history-summary"}function cd(e){return e.revertTarget!==void 0}function dd(e,t){const n=function(r){if(!r)return;const a=r.findLast(o=>Dr(o.turn));return a?a.turn:void 0}(e);if(!((n==null?void 0:n.status)==="success"||(n==null?void 0:n.status)==="failed"||(n==null?void 0:n.status)==="cancelled"))return!1;const s=function(r){return r?r.findLast(o=>{var i;return!((i=o.turn.request_id)!=null&&i.startsWith(Pt))&&Dr(o.turn)}):void 0}(e);return(s==null?void 0:s.turn.request_id)===t.request_id}function md(e){var t;return((t=e.structured_output_nodes)==null?void 0:t.some(n=>n.type===R.TOOL_USE))??!1}function hd(e){var t;return((t=e.structured_request_nodes)==null?void 0:t.some(n=>n.type===L.TOOL_RESULT))??!1}function pd(e){return!(!e||typeof e!="object")&&(!("request_id"in e)||typeof e.request_id=="string")&&(!("seen_state"in e)||e.seen_state==="seen"||e.seen_state==="unseen")}function gd(e){return(e==null?void 0:e.status)==="success"||(e==null?void 0:e.status)==="failed"||(e==null?void 0:e.status)==="cancelled"}function fd(e){if(!e)return;const t=e.filter(n=>$(n.turn)).map(n=>{return"response_text"in(s=n.turn)?s.response_text??"":"";var s}).filter(n=>n.length>0);return t.length>0?t.join(`
`):void 0}async function*Ru(e,t=1e3){for(;e>0;)yield e,await new Promise(n=>setTimeout(n,Math.min(t,e))),e-=t}class Au{constructor(t,n,s,r=5,a=4e3,o){c(this,"_isCancelled",!1);this.requestId=t,this.chatMessage=n,this.startStreamFn=s,this.maxRetries=r,this.baseDelay=a,this.flags=o}cancel(){this._isCancelled=!0}async*getStream(){let t=0,n=0,s=!1;try{for(;!this._isCancelled;){const r=this.startStreamFn({...this.chatMessage,createdTimestamp:Date.now()},this.flags?{flags:this.flags}:void 0);let a,o,i=!1,l=!0;for await(const u of r){if(u.status===I.failed){if(u.isRetriable!==!0||s)return yield u;i=!0,l=u.shouldBackoff??!0,a=u.display_error_message,o=u.request_id;break}s=!0,yield u}if(!i)return;if(this._isCancelled)return yield this.createCancelledStatus();if(t++,t>this.maxRetries)return console.error(`Failed after ${this.maxRetries} attempts: ${a}`),void(yield{request_id:o??this.requestId,seen_state:K.unseen,status:I.failed,display_error_message:a,isRetriable:!1});if(l){const u=this.baseDelay*2**n;n++;for await(const d of Ru(u))yield{request_id:this.requestId,status:I.sent,display_error_message:`Service temporarily unavailable. Retrying in ${Math.floor(d/1e3)} seconds... (Attempt ${t} of ${this.maxRetries})`,isRetriable:!0}}yield{request_id:this.requestId,status:I.sent,display_error_message:`Generating response... (Attempt ${t+1})`,isRetriable:!0}}this._isCancelled&&(yield this.createCancelledStatus())}catch(r){console.error("Unexpected error in chat stream:",r),yield{request_id:this.requestId,seen_state:K.unseen,status:I.failed,display_error_message:r instanceof Error?r.message:String(r)}}}createCancelledStatus(){return{request_id:this.requestId,seen_state:K.unseen,status:I.cancelled}}}var je=(e=>(e.getHydratedTaskRequest="get-hydrated-task-request",e.getHydratedTaskResponse="get-hydrated-task-response",e.setCurrentRootTaskUuid="set-current-root-task-uuid",e.createTaskRequest="create-task-request",e.createTaskResponse="create-task-response",e.updateTaskRequest="update-task-request",e.updateTaskResponse="update-task-response",e.updateHydratedTaskRequest="update-hydrated-task-request",e.updateHydratedTaskResponse="update-hydrated-task-response",e))(je||{});class Ou{constructor(t){c(this,"getHydratedTask",async t=>{const n={type:je.getHydratedTaskRequest,data:{uuid:t}};return(await this._asyncMsgSender.sendToSidecar(n,3e4)).data.task});c(this,"createTask",async(t,n,s)=>{const r={type:je.createTaskRequest,data:{name:t,description:n,parentTaskUuid:s}};return(await this._asyncMsgSender.sendToSidecar(r,3e4)).data.uuid});c(this,"updateTask",async(t,n,s)=>{const r={type:je.updateTaskRequest,data:{uuid:t,updates:n,updatedBy:s}};await this._asyncMsgSender.sendToSidecar(r,3e4)});c(this,"setCurrentRootTaskUuid",t=>{const n={type:je.setCurrentRootTaskUuid,data:{uuid:t}};this._asyncMsgSender.sendToSidecar(n)});c(this,"updateHydratedTask",async(t,n)=>{const s={type:je.updateHydratedTaskRequest,data:{task:t,updatedBy:n}};return(await this._asyncMsgSender.sendToSidecar(s,3e4)).data});this._asyncMsgSender=t}}var oo=(e=>(e.getRulesListRequest="get-rules-list-request",e.getRulesListResponse="get-rules-list-response",e.createRule="create-rule",e.createRuleResponse="create-rule-response",e.openRule="open-rule",e.openGuidelines="open-guidelines",e.deleteRule="delete-rule",e.updateRuleFile="update-rule-file",e.updateRuleFileResponse="update-rule-file-response",e.getWorkspaceRoot="get-workspace-root",e.getWorkspaceRootResponse="get-workspace-root-response",e.autoImportRules="auto-import-rules",e.autoImportRulesOptionsResponse="auto-import-rules-options-response",e.autoImportRulesSelectionRequest="auto-import-rules-selection-request",e.autoImportRulesResponse="auto-import-rules-response",e.processSelectedPathsRequest="process-selected-paths-request",e.processSelectedPathsResponse="process-selected-paths-response",e))(oo||{}),Lt=(e=>(e.loadConversationToolUseStatesRequest="load-conversation-tooluse-states-request",e.loadConversationToolUseStatesResponse="load-conversation-tooluse-states-response",e.saveToolUseStatesRequest="save-tooluse-states-request",e.saveToolUseStatesResponse="save-tooluse-states-response",e.deleteConversationToolUseStatesRequest="delete-conversation-tooluse-states-request",e.deleteConversationToolUseStatesResponse="delete-conversation-tooluse-states-response",e))(Lt||{});class yd{constructor(t,n,s){c(this,"_taskClient");c(this,"getChatInitData",async()=>{const t=await this._asyncMsgSender.send({type:v.chatLoaded},3e4);if(t.data.enableDebugFeatures)try{console.log("Running hello world test...");const n=await async function(s){return(await Hi(Vi,new Ua({sendMessage:a=>{s.postMessage(a)},onReceiveMessage:a=>{const o=i=>{a(i.data)};return window.addEventListener("message",o),()=>{window.removeEventListener("message",o)}}})).testMethod({foo:"bar"},{timeoutMs:1e3})).result}(this._host);console.log("Hello world result:",n)}catch(n){console.error("Hello world error:",n)}return t.data});c(this,"reportWebviewClientEvent",t=>{this._asyncMsgSender.send({type:v.reportWebviewClientMetric,data:{webviewName:La.chat,client_metric:t,value:1}})});c(this,"trackEventWithTypes",(t,n)=>{this._asyncMsgSender.send({type:v.trackAnalyticsEvent,data:{eventName:t,properties:n}})});c(this,"reportAgentSessionEvent",t=>{this._asyncMsgSender.sendToSidecar({type:U.reportAgentSessionEvent,data:t})});c(this,"reportAgentRequestEvent",t=>{this._asyncMsgSender.sendToSidecar({type:U.reportAgentRequestEvent,data:t})});c(this,"getSuggestions",async(t,n=!1)=>{const s={rootPath:"",relPath:t},r=this.findFiles(s,6),a=this.findRecentlyOpenedFiles(s,6),o=this.findFolders(s,3),i=this.findExternalSources(t,n),l=this._flags.enableRules?this.findRules(t,6):Promise.resolve([]),[u,d,m,h,p]=await Promise.all([st(r,[]),st(a,[]),st(o,[]),st(i,[]),st(l,[])]),f=(_,E)=>({...Tl(_),[E]:_}),b=[...u.map(_=>f(_,"file")),...m.map(_=>f(_,"folder")),...d.map(_=>f(_,"recentFile")),...h.map(_=>({label:_.name,name:_.name,id:_.id,externalSource:_})),...p.map(_=>({...Sl(_),rule:_}))];if(this._flags.enablePersonalities){const _=this.getPersonalities(t);_.length>0&&b.push(..._)}return b});c(this,"getPersonalities",t=>{if(!this._flags.enablePersonalities)return[];if(t==="")return fr;const n=t.toLowerCase();return fr.filter(s=>{const r=s.personality.description.toLowerCase(),a=s.label.toLowerCase();return r.includes(n)||a.includes(n)})});c(this,"sendAction",t=>{this._host.postMessage({type:v.mainPanelPerformAction,data:t})});c(this,"showAugmentPanel",()=>{this._asyncMsgSender.send({type:v.showAugmentPanel})});c(this,"showNotification",t=>{this._host.postMessage({type:v.showNotification,data:t})});c(this,"openConfirmationModal",async t=>(await this._asyncMsgSender.send({type:v.openConfirmationModal,data:t},1e9)).data.ok);c(this,"clearMetadataFor",t=>{this._host.postMessage({type:v.chatClearMetadata,data:t})});c(this,"resolvePath",async(t,n=void 0)=>{const s=await this._asyncMsgSender.send({type:v.resolveFileRequest,data:{...t,exactMatch:!0,maxResults:1,searchScope:n}},5e3);if(s.data)return s.data});c(this,"resolveSymbols",async(t,n)=>(await this._asyncMsgSender.send({type:v.findSymbolRequest,data:{query:t,searchScope:n}},3e4)).data);c(this,"getDiagnostics",async()=>(await this._asyncMsgSender.send({type:v.getDiagnosticsRequest},1e3)).data);c(this,"findFiles",async(t,n=12)=>(await this._asyncMsgSender.send({type:v.findFileRequest,data:{...t,maxResults:n}},5e3)).data);c(this,"findFolders",async(t,n=12)=>(await this._asyncMsgSender.send({type:v.findFolderRequest,data:{...t,maxResults:n}},5e3)).data);c(this,"findRecentlyOpenedFiles",async(t,n=12)=>(await this._asyncMsgSender.send({type:v.findRecentlyOpenedFilesRequest,data:{...t,maxResults:n}},5e3)).data);c(this,"findExternalSources",async(t,n=!1)=>this._flags.enableExternalSourcesInChat?n?[]:(await this._asyncMsgSender.send({type:v.findExternalSourcesRequest,data:{query:t,source_types:[]}},5e3)).data.sources??[]:[]);c(this,"findRules",async(t,n=12)=>(await this._asyncMsgSender.sendToSidecar({type:oo.getRulesListRequest,data:{query:t,maxResults:n}})).data.rules);c(this,"openFile",t=>{this._host.postMessage({type:v.openFile,data:t})});c(this,"saveFile",t=>this._host.postMessage({type:v.saveFile,data:t}));c(this,"loadFile",t=>this._host.postMessage({type:v.loadFile,data:t}));c(this,"openMemoriesFile",()=>{this._host.postMessage({type:v.openMemoriesFile})});c(this,"canShowTerminal",async(t,n)=>{try{return(await this._asyncMsgSender.send({type:v.canShowTerminal,data:{terminalId:t,command:n}},5e3)).data.canShow}catch(s){return console.error("Failed to check if terminal can be shown:",s),!1}});c(this,"showTerminal",async(t,n)=>{try{return(await this._asyncMsgSender.send({type:v.showTerminal,data:{terminalId:t,command:n}},5e3)).data.success}catch(s){return console.error("Failed to show terminal:",s),!1}});c(this,"createFile",(t,n)=>{this._host.postMessage({type:v.chatCreateFile,data:{code:t,relPath:n}})});c(this,"openScratchFile",async(t,n="shellscript")=>{await this._asyncMsgSender.send({type:v.openScratchFileRequest,data:{content:t,language:n}},1e4)});c(this,"resolveWorkspaceFileChunk",async t=>{try{return(await this._asyncMsgSender.send({type:v.resolveWorkspaceFileChunkRequest,data:t},5e3)).data}catch{return}});c(this,"smartPaste",t=>{this._host.postMessage({type:v.chatSmartPaste,data:t})});c(this,"getHydratedTask",async t=>this._taskClient.getHydratedTask(t));c(this,"updateHydratedTask",async(t,n)=>this._taskClient.updateHydratedTask(t,n));c(this,"setCurrentRootTaskUuid",t=>{this._taskClient.setCurrentRootTaskUuid(t)});c(this,"createTask",async(t,n,s)=>this._taskClient.createTask(t,n,s));c(this,"updateTask",async(t,n,s)=>this._taskClient.updateTask(t,n,s));c(this,"saveChat",async(t,n,s)=>this._asyncMsgSender.send({type:v.saveChat,data:{conversationId:t,chatHistory:n,title:s}},5e3));c(this,"updateUserGuidelines",t=>{this._host.postMessage({type:v.updateUserGuidelines,data:t})});c(this,"updateWorkspaceGuidelines",t=>{this._host.postMessage({type:v.updateWorkspaceGuidelines,data:t})});c(this,"openSettingsPage",t=>{this._host.postMessage({type:v.openSettingsPage,data:t})});c(this,"_activeRetryStreams",new Map);c(this,"cancelChatStream",async t=>{var n;(n=this._activeRetryStreams.get(t))==null||n.cancel(),await this._asyncMsgSender.send({type:v.chatUserCancel,data:{requestId:t}},1e4)});c(this,"sendUserRating",async(t,n,s,r="")=>{const a={requestId:t,rating:s,note:r,mode:n},o={type:v.chatRating,data:a};return(await this._asyncMsgSender.send(o,3e4)).data});c(this,"triggerUsedChatMetric",()=>{this._host.postMessage({type:v.usedChat})});c(this,"createProject",t=>{this._host.postMessage({type:v.mainPanelCreateProject,data:{name:t}})});c(this,"openProjectFolder",()=>{this._host.postMessage({type:v.mainPanelPerformAction,data:"open-folder"})});c(this,"closeProjectFolder",()=>{this._host.postMessage({type:v.mainPanelPerformAction,data:"close-folder"})});c(this,"cloneRepository",()=>{this._host.postMessage({type:v.mainPanelPerformAction,data:"clone-repository"})});c(this,"grantSyncPermission",()=>{this._host.postMessage({type:v.mainPanelPerformAction,data:"grant-sync-permission"})});c(this,"startRemoteMCPAuth",t=>{this._host.postMessage({type:v.startRemoteMCPAuth,data:{name:t}})});c(this,"callTool",async(t,n,s,r,a,o)=>{const i={type:v.callTool,data:{chatRequestId:t,toolUseId:n,name:s,input:r,chatHistory:a,conversationId:o}};return(await this._asyncMsgSender.send(i,0)).data});c(this,"cancelToolRun",async(t,n)=>{const s={type:v.cancelToolRun,data:{requestId:t,toolUseId:n}};await this._asyncMsgSender.send(s,0)});c(this,"checkSafe",async t=>{const n={type:Ft.checkToolCallSafeRequest,data:t};return(await this._asyncMsgSender.sendToSidecar(n,0)).data});c(this,"closeAllToolProcesses",async()=>{await this._asyncMsgSender.sendToSidecar({type:Ft.closeAllToolProcesses},0)});c(this,"getToolIdentifier",async t=>{const n={type:Ft.getToolIdentifierRequest,data:{toolName:t}};return(await this._asyncMsgSender.sendToSidecar(n,0)).data});c(this,"getChatMode",async()=>{const t={type:U.getChatModeRequest};return(await this._asyncMsgSender.sendToSidecar(t,3e4)).data.chatMode});c(this,"setChatMode",t=>{this._asyncMsgSender.send({type:v.chatModeChanged,data:{mode:t}})});c(this,"getAgentEditList",async(t,n)=>{const s={type:U.getEditListRequest,data:{fromTimestamp:t,toTimestamp:n}};return(await this._asyncMsgSender.sendToSidecar(s,3e4)).data});c(this,"hasChangesSince",async t=>{const n={type:U.getEditListRequest,data:{fromTimestamp:t,toTimestamp:Number.MAX_SAFE_INTEGER}};return(await this._asyncMsgSender.sendToSidecar(n,3e4)).data.edits.filter(s=>{var r,a;return((r=s.changesSummary)==null?void 0:r.totalAddedLines)||((a=s.changesSummary)==null?void 0:a.totalRemovedLines)}).length>0});c(this,"getToolCallCheckpoint",async t=>{const n={type:v.getToolCallCheckpoint,data:{requestId:t}};return(await this._asyncMsgSender.send(n,3e4)).data.checkpointNumber});c(this,"setCurrentConversation",t=>{this._asyncMsgSender.sendToSidecar({type:U.setCurrentConversation,data:{conversationId:t}})});c(this,"migrateConversationId",async(t,n)=>{await this._asyncMsgSender.sendToSidecar({type:U.migrateConversationId,data:{oldConversationId:t,newConversationId:n}},3e4)});c(this,"showAgentReview",(t,n,s,r=!0,a)=>{this._asyncMsgSender.sendToSidecar({type:U.chatReviewAgentFile,data:{qualifiedPathName:t,fromTimestamp:n,toTimestamp:s,retainFocus:r,useNativeDiffIfAvailable:a}})});c(this,"acceptAllAgentEdits",async()=>(await this._asyncMsgSender.sendToSidecar({type:U.chatAgentEditAcceptAll}),!0));c(this,"revertToTimestamp",async(t,n)=>(await this._asyncMsgSender.sendToSidecar({type:U.revertToTimestamp,data:{timestamp:t,qualifiedPathNames:n}}),!0));c(this,"getAgentOnboardingPrompt",async()=>(await this._asyncMsgSender.send({type:v.chatGetAgentOnboardingPromptRequest,data:{}},3e4)).data.prompt);c(this,"getAgentEditChangesByRequestId",async t=>{const n={type:U.getEditChangesByRequestIdRequest,data:{requestId:t}};return(await this._asyncMsgSender.sendToSidecar(n,3e4)).data});c(this,"getAgentEditContentsByRequestId",async t=>{const n={type:U.getAgentEditContentsByRequestId,data:{requestId:t}};return(await this._asyncMsgSender.sendToSidecar(n,3e4)).data});c(this,"triggerInitialOrientation",()=>{this._host.postMessage({type:v.triggerInitialOrientation})});c(this,"getWorkspaceInfo",async()=>{try{return(await this._asyncMsgSender.send({type:v.getWorkspaceInfoRequest},5e3)).data}catch(t){return console.error("Error getting workspace info:",t),{}}});c(this,"toggleCollapseUnchangedRegions",()=>{this._host.postMessage({type:v.toggleCollapseUnchangedRegions})});c(this,"checkAgentAutoModeApproval",async()=>(await this._asyncMsgSender.send({type:v.checkAgentAutoModeApproval},5e3)).data);c(this,"setAgentAutoModeApproved",async t=>{await this._asyncMsgSender.send({type:v.setAgentAutoModeApproved,data:t},5e3)});c(this,"checkHasEverUsedAgent",async()=>(await this._asyncMsgSender.sendToSidecar({type:U.checkHasEverUsedAgent},5e3)).data);c(this,"setHasEverUsedAgent",async t=>{await this._asyncMsgSender.sendToSidecar({type:U.setHasEverUsedAgent,data:t},5e3)});c(this,"checkHasEverUsedRemoteAgent",async()=>(await this._asyncMsgSender.sendToSidecar({type:U.checkHasEverUsedRemoteAgent},5e3)).data);c(this,"setHasEverUsedRemoteAgent",async t=>{await this._asyncMsgSender.sendToSidecar({type:U.setHasEverUsedRemoteAgent,data:t},5e3)});c(this,"getChatRequestIdeState",async()=>{const t={type:v.getChatRequestIdeStateRequest};return(await this._asyncMsgSender.send(t,3e4)).data});c(this,"reportError",t=>{this._host.postMessage({type:v.reportError,data:t})});c(this,"sendMemoryCreated",async t=>{await this._asyncMsgSender.sendToSidecar(t,5e3)});c(this,"sendGitMessage",async t=>await this._asyncMsgSender.sendToSidecar(t,3e4));this._host=t,this._asyncMsgSender=n,this._flags=s,this._taskClient=new Ou(n)}async*generateCommitMessage(){const t={type:v.generateCommitMessage},n=this._asyncMsgSender.stream(t,3e4,6e4);yield*Mn(n,()=>{},this._flags.retryChatStreamTimeouts)}async*sendInstructionMessage(t,n){const s={instruction:t.request_message??"",selectedCodeDetails:n,requestId:t.request_id},r={type:v.chatInstructionMessage,data:s},a=this._asyncMsgSender.stream(r,3e4,6e4);yield*async function*(o){let i;try{for await(const l of o)i=l.data.requestId,yield{request_id:i,response_text:l.data.text,seen_state:K.unseen,status:I.sent};yield{request_id:i,seen_state:K.unseen,status:I.success}}catch(l){console.error("Error in chat instruction model reply stream:",l),yield{request_id:i,seen_state:K.unseen,status:I.failed}}}(a)}async openGuidelines(t){this._host.postMessage({type:v.openGuidelines,data:t})}async*getExistingChatStream(t,n,s){const r=s==null?void 0:s.flags.enablePreferenceCollection,a=r?1e9:6e4,o=r?1e9:3e5,i={type:v.chatGetStreamRequest,data:{requestId:t,lastChunkId:n}},l=this._asyncMsgSender.stream(i,a,o);yield*Mn(l,this.reportError,this._flags.retryChatStreamTimeouts)}async*startChatStream(t,n){const s=n==null?void 0:n.flags.enablePreferenceCollection,r=s?1e9:1e5,a=s?1e9:3e5,o={type:v.chatUserMessage,data:t},i=this._asyncMsgSender.stream(o,r,a);yield*Mn(i,this.reportError,this._flags.retryChatStreamTimeouts)}async checkToolExists(t){return(await this._asyncMsgSender.send({type:v.checkToolExists,toolName:t},0)).exists}async saveImage(t,n){const s=pr(await En(t)),r=n??`${await hr(await Tn(s))}.${t.name.split(".").at(-1)}`;return(await this._asyncMsgSender.send({type:v.chatSaveImageRequest,data:{filename:r,data:s}},1e4)).data}async saveAttachment(t,n){const s=pr(await En(t)),r=n??`${await hr(await Tn(s))}.${t.name.split(".").at(-1)}`;return(await this._asyncMsgSender.send({type:v.chatSaveAttachmentRequest,data:{filename:r,data:s}},1e4)).data}async loadImage(t){const n=await this._asyncMsgSender.send({type:v.chatLoadImageRequest,data:t},1e4),s=n.data?await Tn(n.data):void 0;if(!s)return;let r="application/octet-stream";const a=t.split(".").at(-1);a==="png"?r="image/png":a!=="jpg"&&a!=="jpeg"||(r="image/jpeg");const o=new File([s],t,{type:r});return await En(o)}async deleteImage(t){await this._asyncMsgSender.send({type:v.chatDeleteImageRequest,data:t},1e4)}async*startChatStreamWithRetry(t,n,s){const r=new Au(t,n,(a,o)=>this.startChatStream(a,o),(s==null?void 0:s.maxRetries)??5,4e3,s==null?void 0:s.flags);this._activeRetryStreams.set(t,r);try{yield*r.getStream()}finally{this._activeRetryStreams.delete(t)}}async getSubscriptionInfo(){return await this._asyncMsgSender.send({type:v.getSubscriptionInfo},5e3)}async loadExchanges(t,n){if(n.length===0)return[];const s={type:Ut.loadExchangesByUuidsRequest,data:{conversationId:t,uuids:n}};return(await this._asyncMsgSender.sendToSidecar(s,3e4)).data.exchanges}async saveExchanges(t,n){if(n.length===0)return;const s={type:Ut.saveExchangesRequest,data:{conversationId:t,exchanges:n}};await this._asyncMsgSender.sendToSidecar(s,3e4)}async deleteConversationExchanges(t){const n={type:Ut.deleteConversationExchangesRequest,data:{conversationId:t}};await this._asyncMsgSender.sendToSidecar(n,3e4)}async loadConversationToolUseStates(t){const n={type:Lt.loadConversationToolUseStatesRequest,data:{conversationId:t}};return(await this._asyncMsgSender.sendToSidecar(n,3e4)).data.toolUseStates}async saveToolUseStates(t,n){if(Object.keys(n).length===0)return;const s={type:Lt.saveToolUseStatesRequest,data:{conversationId:t,toolUseStates:n}};await this._asyncMsgSender.sendToSidecar(s,3e4)}async deleteConversationToolUseStates(t){const n={type:Lt.deleteConversationToolUseStatesRequest,data:{conversationId:t}};await this._asyncMsgSender.sendToSidecar(n,3e4)}}async function*Mn(e,t=()=>{},n){let s;try{for await(const r of e){if(s=r.data.requestId,r.data.error)return console.error("Error in chat model reply stream:",r.data.error.displayErrorMessage),yield{request_id:s,seen_state:K.unseen,status:I.failed,display_error_message:r.data.error.displayErrorMessage,isRetriable:r.data.error.isRetriable,shouldBackoff:r.data.error.shouldBackoff};const a={request_id:s,response_text:r.data.text,workspace_file_chunks:r.data.workspaceFileChunks,structured_output_nodes:Mu(r.data.nodes),seen_state:K.unseen,status:I.sent,lastChunkId:r.data.chunkId};r.data.stop_reason!=null&&(a.stop_reason=r.data.stop_reason),yield a}yield{request_id:s,seen_state:K.unseen,status:I.success}}catch(r){let a,o;if(t({originalRequestId:s||"",sanitizedMessage:r instanceof Error?r.message:String(r),stackTrace:r instanceof Error&&r.stack||"",diagnostics:[{key:"error_class",value:"Extension-WebView Error"}]}),r instanceof co&&n)switch(r.name){case"MessageTimeout":a=!0,o=!1;break;case"StreamTimeout":case"InvalidResponse":a=!1}console.error("Unexpected error in chat model reply stream:",r),yield{request_id:s,seen_state:K.unseen,status:I.failed,isRetriable:a,shouldBackoff:o}}}async function st(e,t){try{return await e}catch(n){return console.warn(`Error while resolving promise: ${n}`),t}}function Mu(e){if(!e)return e;let t=!1;return e.filter(n=>n.type!==R.TOOL_USE||!t&&(t=!0,!0))}const _d=15,bd=1e3,Du=25e4,vd=2e4;class Ed{constructor(t){c(this,"_enableEditableHistory",!1);c(this,"_enablePreferenceCollection",!1);c(this,"_enableRetrievalDataCollection",!1);c(this,"_enableDebugFeatures",!1);c(this,"_enableConversationDebugUtils",!1);c(this,"_enableRichTextHistory",!1);c(this,"_enableAgentSwarmMode",!1);c(this,"_modelDisplayNameToId",{});c(this,"_fullFeatured",!0);c(this,"_enableExternalSourcesInChat",!1);c(this,"_smallSyncThreshold",15);c(this,"_bigSyncThreshold",1e3);c(this,"_enableSmartPaste",!1);c(this,"_enableDirectApply",!1);c(this,"_summaryTitles",!1);c(this,"_suggestedEditsAvailable",!1);c(this,"_enableShareService",!1);c(this,"_maxTrackableFileCount",Du);c(this,"_enableDesignSystemRichTextEditor",!1);c(this,"_enableSources",!1);c(this,"_enableChatMermaidDiagrams",!1);c(this,"_smartPastePrecomputeMode",mo.visibleHover);c(this,"_useNewThreadsMenu",!1);c(this,"_enableChatMermaidDiagramsMinVersion",!1);c(this,"_enablePromptEnhancer",!1);c(this,"_idleNewSessionNotificationTimeoutMs");c(this,"_idleNewSessionMessageTimeoutMs");c(this,"_enableChatMultimodal",!1);c(this,"_enableAgentMode",!1);c(this,"_enableAgentAutoMode",!1);c(this,"_enableRichCheckpointInfo",!1);c(this,"_agentMemoriesFilePathName");c(this,"_conversationHistorySizeThresholdBytes",44040192);c(this,"_userTier","unknown");c(this,"_eloModelConfiguration",{highPriorityModels:[],regularBattleModels:[],highPriorityThreshold:.5});c(this,"_truncateChatHistory",!1);c(this,"_enableBackgroundAgents",!1);c(this,"_enableNewThreadsList",!1);c(this,"_customPersonalityPrompts",{});c(this,"_enablePersonalities",!1);c(this,"_enableRules",!1);c(this,"_memoryClassificationOnFirstToken",!1);c(this,"_enableGenerateCommitMessage",!1);c(this,"_modelRegistry",{});c(this,"_enableModelRegistry",!1);c(this,"_enableTaskList",!1);c(this,"_clientAnnouncement","");c(this,"_useHistorySummary",!1);c(this,"_historySummaryParams","");c(this,"_enableExchangeStorage",!1);c(this,"_enableToolUseStateStorage",!1);c(this,"_retryChatStreamTimeouts",!1);c(this,"_enableCommitIndexing",!1);c(this,"_enableMemoryRetrieval",!1);c(this,"_enableAgentTabs",!1);c(this,"_isVscodeVersionOutdated",!1);c(this,"_vscodeMinVersion","");c(this,"_enableGroupedTools",!1);c(this,"_remoteAgentsResumeHintAvailableTtlDays",0);c(this,"_enableParallelTools",!1);c(this,"_enableAgentGitTracker",!1);c(this,"_memoriesParams",{});c(this,"_subscribers",new Set);c(this,"subscribe",t=>(this._subscribers.add(t),t(this),()=>{this._subscribers.delete(t)}));c(this,"update",t=>{this._enableEditableHistory=t.enableEditableHistory??this._enableEditableHistory,this._enablePreferenceCollection=t.enablePreferenceCollection??this._enablePreferenceCollection,this._enableRetrievalDataCollection=t.enableRetrievalDataCollection??this._enableRetrievalDataCollection,this._enableDebugFeatures=t.enableDebugFeatures??this._enableDebugFeatures,this._enableConversationDebugUtils=t.enableConversationDebugUtils??this._enableConversationDebugUtils,this._enableRichTextHistory=t.enableRichTextHistory??this._enableRichTextHistory,this._enableAgentSwarmMode=t.enableAgentSwarmMode??this._enableAgentSwarmMode,this._modelDisplayNameToId={...t.modelDisplayNameToId},this._fullFeatured=t.fullFeatured??this._fullFeatured,this._enableExternalSourcesInChat=t.enableExternalSourcesInChat??this._enableExternalSourcesInChat,this._smallSyncThreshold=t.smallSyncThreshold??this._smallSyncThreshold,this._bigSyncThreshold=t.bigSyncThreshold??this._bigSyncThreshold,this._enableSmartPaste=t.enableSmartPaste??this._enableSmartPaste,this._enableDirectApply=t.enableDirectApply??this._enableDirectApply,this._summaryTitles=t.summaryTitles??this._summaryTitles,this._suggestedEditsAvailable=t.suggestedEditsAvailable??this._suggestedEditsAvailable,this._enableShareService=t.enableShareService??this._enableShareService,this._maxTrackableFileCount=t.maxTrackableFileCount??this._maxTrackableFileCount,this._enableDesignSystemRichTextEditor=t.enableDesignSystemRichTextEditor??this._enableDesignSystemRichTextEditor,this._enableSources=t.enableSources??this._enableSources,this._enableChatMermaidDiagrams=t.enableChatMermaidDiagrams??this._enableChatMermaidDiagrams,this._smartPastePrecomputeMode=t.smartPastePrecomputeMode??this._smartPastePrecomputeMode,this._useNewThreadsMenu=t.useNewThreadsMenu??this._useNewThreadsMenu,this._enableChatMermaidDiagramsMinVersion=t.enableChatMermaidDiagramsMinVersion??this._enableChatMermaidDiagramsMinVersion,this._enablePromptEnhancer=t.enablePromptEnhancer??this._enablePromptEnhancer,this._idleNewSessionMessageTimeoutMs=t.idleNewSessionMessageTimeoutMs??(t.enableDebugFeatures?this._idleNewSessionMessageTimeoutMs??3e5:this._idleNewSessionMessageTimeoutMs),this._idleNewSessionNotificationTimeoutMs=t.idleNewSessionNotificationTimeoutMs??0,this._enableChatMultimodal=t.enableChatMultimodal??this._enableChatMultimodal,this._enableAgentMode=t.enableAgentMode??this._enableAgentMode,this._enableAgentAutoMode=t.enableAgentAutoMode??this._enableAgentAutoMode,this._enableRichCheckpointInfo=t.enableRichCheckpointInfo??this._enableRichCheckpointInfo,this._agentMemoriesFilePathName=t.agentMemoriesFilePathName??this._agentMemoriesFilePathName,this._conversationHistorySizeThresholdBytes=t.conversationHistorySizeThresholdBytes??this._conversationHistorySizeThresholdBytes,this._userTier=t.userTier??this._userTier,this._eloModelConfiguration=t.eloModelConfiguration??this._eloModelConfiguration,this._truncateChatHistory=t.truncateChatHistory??this._truncateChatHistory,this._enableBackgroundAgents=t.enableBackgroundAgents??this._enableBackgroundAgents,this._enableNewThreadsList=t.enableNewThreadsList??this._enableNewThreadsList,this._customPersonalityPrompts=t.customPersonalityPrompts??this._customPersonalityPrompts,this._enablePersonalities=t.enablePersonalities??this._enablePersonalities,this._enableRules=t.enableRules??this._enableRules,this._memoryClassificationOnFirstToken=t.memoryClassificationOnFirstToken??this._memoryClassificationOnFirstToken,this._enableGenerateCommitMessage=t.enableGenerateCommitMessage??this._enableGenerateCommitMessage,this._modelRegistry=t.modelRegistry??this._modelRegistry,this._enableModelRegistry=t.enableModelRegistry??this._enableModelRegistry,this._enableTaskList=t.enableTaskList??this._enableTaskList,this._clientAnnouncement=t.clientAnnouncement??this._clientAnnouncement,this._useHistorySummary=t.useHistorySummary??this._useHistorySummary,this._historySummaryParams=t.historySummaryParams??this._historySummaryParams,this._enableExchangeStorage=t.enableExchangeStorage??this._enableExchangeStorage,this._retryChatStreamTimeouts=t.retryChatStreamTimeouts??this._retryChatStreamTimeouts,this._enableCommitIndexing=t.enableCommitIndexing??this._enableCommitIndexing,this._enableMemoryRetrieval=t.enableMemoryRetrieval??this._enableMemoryRetrieval,this._enableAgentTabs=t.enableAgentTabs??this._enableAgentTabs,this._isVscodeVersionOutdated=t.isVscodeVersionOutdated??this._isVscodeVersionOutdated,this._vscodeMinVersion=t.vscodeMinVersion??this._vscodeMinVersion,this._enableGroupedTools=t.enableGroupedTools??this._enableGroupedTools,this._remoteAgentsResumeHintAvailableTtlDays=t.remoteAgentsResumeHintAvailableTtlDays??this._remoteAgentsResumeHintAvailableTtlDays,this._enableToolUseStateStorage=t.enableToolUseStateStorage??this._enableToolUseStateStorage,this._enableParallelTools=t.enableParallelTools??this._enableParallelTools,this._enableAgentGitTracker=t.enableAgentGitTracker??this._enableAgentGitTracker,this._memoriesParams=t.memoriesParams??this._memoriesParams,this._subscribers.forEach(n=>n(this))});c(this,"isModelIdValid",t=>t!==void 0&&(Object.values(this._modelDisplayNameToId).includes(t)||Object.values(this._modelRegistry).includes(t??"")));c(this,"getModelDisplayName",t=>{if(t!==void 0)return Object.keys(this._modelDisplayNameToId).find(n=>this._modelDisplayNameToId[n]===t)});t&&this.update(t)}get enableEditableHistory(){return this._fullFeatured&&(this._enableEditableHistory||this._enableDebugFeatures)}get enablePreferenceCollection(){return this._enablePreferenceCollection}get enableRetrievalDataCollection(){return this._enableRetrievalDataCollection}get enableDebugFeatures(){return this._enableDebugFeatures}get enableConversationDebugUtils(){return this._enableConversationDebugUtils||this._enableDebugFeatures}get enableGenerateCommitMessage(){return this._enableGenerateCommitMessage}get enableRichTextHistory(){return this._enableRichTextHistory||this._enableDebugFeatures}get enableAgentSwarmMode(){return this._enableAgentSwarmMode}get modelDisplayNameToId(){return this._modelDisplayNameToId}get orderedModelDisplayNames(){return Object.keys(this._modelDisplayNameToId).sort((t,n)=>{const s=t.toLowerCase(),r=n.toLowerCase();return s==="default"&&r!=="default"?-1:r==="default"&&s!=="default"?1:t.localeCompare(n)})}get fullFeatured(){return this._fullFeatured}get enableExternalSourcesInChat(){return this._enableExternalSourcesInChat}get smallSyncThreshold(){return this._smallSyncThreshold}get bigSyncThreshold(){return this._bigSyncThreshold}get enableSmartPaste(){return this._enableDebugFeatures||this._enableSmartPaste}get enableDirectApply(){return this._enableDirectApply||this._enableDebugFeatures}get enableShareService(){return this._enableShareService}get summaryTitles(){return this._summaryTitles}get suggestedEditsAvailable(){return this._suggestedEditsAvailable}get maxTrackableFileCount(){return this._maxTrackableFileCount}get enableSources(){return this._enableDebugFeatures||this._enableSources}get enableChatMermaidDiagrams(){return this._enableDebugFeatures||this._enableChatMermaidDiagrams}get smartPastePrecomputeMode(){return this._smartPastePrecomputeMode}get useNewThreadsMenu(){return this._useNewThreadsMenu}get enableChatMermaidDiagramsMinVersion(){return this._enableChatMermaidDiagramsMinVersion}get enablePromptEnhancer(){return this._enablePromptEnhancer}get enableDesignSystemRichTextEditor(){return this._enableDesignSystemRichTextEditor}get idleNewSessionNotificationTimeoutMs(){return this._idleNewSessionNotificationTimeoutMs??0}get idleNewSessionMessageTimeoutMs(){return this._idleNewSessionMessageTimeoutMs??0}get enableChatMultimodal(){return this._enableChatMultimodal}get enableAgentMode(){return this._enableAgentMode}get enableAgentAutoMode(){return this._enableAgentAutoMode}get enableRichCheckpointInfo(){return this._enableRichCheckpointInfo}get agentMemoriesFilePathName(){return this._agentMemoriesFilePathName}get conversationHistorySizeThresholdBytes(){return this._conversationHistorySizeThresholdBytes}get userTier(){return this._userTier}get eloModelConfiguration(){return this._eloModelConfiguration}get truncateChatHistory(){return this._truncateChatHistory}get enableBackgroundAgents(){return this._enableBackgroundAgents}get enableNewThreadsList(){return this._enableNewThreadsList}get customPersonalityPrompts(){return this._customPersonalityPrompts}get enablePersonalities(){return this._enablePersonalities||this._enableDebugFeatures}get enableRules(){return this._enableRules}get memoryClassificationOnFirstToken(){return this._memoryClassificationOnFirstToken}get modelRegistry(){return this._modelRegistry}get enableModelRegistry(){return this._enableModelRegistry}get enableTaskList(){return this._enableTaskList}get clientAnnouncement(){return this._clientAnnouncement}get useHistorySummary(){return this._useHistorySummary}get historySummaryParams(){return this._historySummaryParams}get enableExchangeStorage(){return this._enableExchangeStorage}get enableToolUseStateStorage(){return this._enableToolUseStateStorage}get retryChatStreamTimeouts(){return this._retryChatStreamTimeouts}get enableCommitIndexing(){return this._enableCommitIndexing}get enableMemoryRetrieval(){return this._enableMemoryRetrieval}get enableAgentTabs(){return this._enableAgentTabs}get isVscodeVersionOutdated(){return this._isVscodeVersionOutdated}get vscodeMinVersion(){return this._vscodeMinVersion}get enableErgonomicsUpdate(){return this._enableDebugFeatures}get enableGroupedTools(){return this._enableGroupedTools}get remoteAgentsResumeHintAvailableTtlDays(){return this._remoteAgentsResumeHintAvailableTtlDays}get enableParallelTools(){return this._enableParallelTools}get enableAgentGitTracker(){return this._enableAgentGitTracker}get memoriesParams(){return this._memoriesParams}}function $u(e){let t,n;return{c(){t=Dn("svg"),n=Dn("path"),N(n,"fill-rule","evenodd"),N(n,"clip-rule","evenodd"),N(n,"d","M8.84182 3.13514C9.04327 3.32401 9.05348 3.64042 8.86462 3.84188L5.43521 7.49991L8.86462 11.1579C9.05348 11.3594 9.04327 11.6758 8.84182 11.8647C8.64036 12.0535 8.32394 12.0433 8.13508 11.8419L4.38508 7.84188C4.20477 7.64955 4.20477 7.35027 4.38508 7.15794L8.13508 3.15794C8.32394 2.95648 8.64036 2.94628 8.84182 3.13514Z"),N(n,"fill","currentColor"),N(t,"width","15"),N(t,"height","15"),N(t,"viewBox","0 0 15 15"),N(t,"fill","none"),N(t,"xmlns","http://www.w3.org/2000/svg")},m(s,r){M(s,t,r),Yr(t,n)},p:ve,i:ve,o:ve,d(s){s&&A(t)}}}class Fu extends ne{constructor(t){super(),se(this,t,null,$u,re,{})}}const ut=class ut{constructor(t=void 0){c(this,"_lastFocusAnchorElement");c(this,"_focusedIndexStore",Yn(void 0));c(this,"focusedIndex",this._focusedIndexStore);c(this,"_rootElement");c(this,"_triggerElement");c(this,"_getItems",()=>{var s;const t=(s=this._rootElement)==null?void 0:s.querySelectorAll(`.${ut.ITEM_CLASS}`),n=t==null?void 0:t[0];return n instanceof HTMLElement&&this._recomputeFocusAnchor(n),Array.from(t??[])});c(this,"_recomputeFocusAnchor",t=>{var a;const n=(a=this._parentContext)==null?void 0:a._getItems(),s=n==null?void 0:n.indexOf(t);if(s===void 0||n===void 0)return;const r=Math.max(s-1,0);this._lastFocusAnchorElement=n[r]});c(this,"registerRoot",t=>{this._rootElement=t,t.addEventListener("keydown",this._onKeyDown);const n=()=>{this.getCurrentFocusedIdx()},s=r=>{t.contains(r.relatedTarget)||this._focusedIndexStore.set(void 0)};return t.addEventListener("focusin",n),t.addEventListener("focusout",s),this._getItems(),{destroy:()=>{this._rootElement=void 0,t.removeEventListener("keydown",this._onKeyDown),t.removeEventListener("focusin",n),t.removeEventListener("focusout",s),this._focusedIndexStore.set(void 0)}}});c(this,"registerTrigger",t=>(this._triggerElement=t.querySelector('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])')??t,{destroy:()=>{this._triggerElement=void 0}}));c(this,"_onKeyDown",t=>{var n;switch(t.key){case"ArrowUp":t.preventDefault(),this.focusPrev();break;case"ArrowDown":t.preventDefault(),this.focusNext();break;case"ArrowLeft":this._requestClose();break;case"ArrowRight":this.clickFocusedItem();break;case"Tab":{const s=this.getCurrentFocusedIdx();if(s===void 0||this.parentContext)break;(!t.shiftKey&&s===this._getItems().length-1||t.shiftKey&&s===0)&&(t.preventDefault(),(n=this._triggerElement)==null||n.focus());break}}});c(this,"_requestClose",()=>{var t;(t=this._rootElement)==null||t.dispatchEvent(new Ao)});c(this,"getCurrentFocusedIdx",()=>{const t=this._getItems().findIndex(s=>s===document.activeElement),n=t===-1?void 0:t;return this._focusedIndexStore.set(n),n});c(this,"setFocusedIdx",t=>{const n=this._getItems();if(n.length===0)return void this._focusedIndexStore.set(void 0);const s=Rt(t,n.length);this._focusedIndexStore.set(s)});c(this,"focusIdx",t=>{const n=this._getItems();if(n.length===0)return void this._focusedIndexStore.set(void 0);const s=Rt(t,n.length),r=n[s];r==null||r.focus(),this._focusedIndexStore.set(s)});c(this,"popNestedFocus",()=>{if(this._parentContext){this._focusedIndexStore.set(void 0);const t=this._lastFocusAnchorElement,n=t?this._parentContext._getItems().indexOf(t):void 0;return n===void 0?(this._parentContext.focusIdx(0),!0):(this._parentContext.focusIdx(n),!0)}return!1});c(this,"focusNext",()=>{const t=this._getItems();if(t.length===0)return;const n=Rt(t.findIndex(s=>s===document.activeElement)+1,t.length);t[n].focus(),this._focusedIndexStore.set(n)});c(this,"focusPrev",()=>{var s;const t=this._getItems();if(t.length===0)return;const n=Rt(t.findIndex(r=>r===document.activeElement)-1,t.length);(s=t[n])==null||s.focus(),this._focusedIndexStore.set(n)});c(this,"clickFocusedItem",async()=>{const t=document.activeElement;t&&(t.click(),await vo())});this._parentContext=t}get rootElement(){return this._rootElement}get triggerElement(){return this._triggerElement}get parentContext(){return this._parentContext}};c(ut,"CONTEXT_KEY","augment-dropdown-menu-focus"),c(ut,"ITEM_CLASS","js-dropdown-menu__focusable-item");let ye=ut;function Rt(e,t){return(e%t+t)%t}function Uu(e){let t,n,s,r,a;const o=e[11].default,i=ue(o,e,e[13],null);return{c(){t=Q("div"),i&&i.c(),N(t,"class",n=Ee(`l-dropdown-menu-augment__contents l-dropdown-menu-augment__contents--size-${e[6]}`)+" svelte-mbbzty")},m(l,u){M(l,t,u),i&&i.m(t,null),s=!0,r||(a=Kr(e[8].registerRoot(t)),r=!0)},p(l,u){i&&i.p&&(!s||8192&u)&&ce(i,o,l,l[13],s?me(o,l[13],u,null):de(l[13]),null),(!s||64&u&&n!==(n=Ee(`l-dropdown-menu-augment__contents l-dropdown-menu-augment__contents--size-${l[6]}`)+" svelte-mbbzty"))&&N(t,"class",n)},i(l){s||(T(i,l),s=!0)},o(l){S(i,l),s=!1},d(l){l&&A(t),i&&i.d(l),r=!1,a()}}}function Pu(e){let t,n,s;return n=new Mo({props:{size:e[6],insetContent:!0,includeBackground:!1,$$slots:{default:[Uu]},$$scope:{ctx:e}}}),{c(){t=Q("div"),Z(n.$$.fragment),N(t,"class","l-dropdown-menu-augment__container svelte-mbbzty")},m(r,a){M(r,t,a),J(n,t,null),s=!0},p(r,a){const o={};64&a&&(o.size=r[6]),8256&a&&(o.$$scope={dirty:a,ctx:r}),n.$set(o)},i(r){s||(T(n.$$.fragment,r),s=!0)},o(r){S(n.$$.fragment,r),s=!1},d(r){r&&A(t),X(n)}}}function Lu(e){let t,n,s,r;return t=new Oo({props:{onEscapeKeyDown:e[0],onClickOutside:e[1],onRequestClose:e[2],side:e[3],align:e[4],$$slots:{default:[Pu]},$$scope:{ctx:e}}}),t.$on("keydown",e[12]),{c(){Z(t.$$.fragment)},m(a,o){J(t,a,o),n=!0,s||(r=pe(window,"keydown",e[9]),s=!0)},p(a,[o]){const i={};1&o&&(i.onEscapeKeyDown=a[0]),2&o&&(i.onClickOutside=a[1]),4&o&&(i.onRequestClose=a[2]),8&o&&(i.side=a[3]),16&o&&(i.align=a[4]),8256&o&&(i.$$scope={dirty:o,ctx:a}),t.$set(i)},i(a){n||(T(t.$$.fragment,a),n=!0)},o(a){S(t.$$.fragment,a),n=!1},d(a){X(t,a),s=!1,r()}}}const bt="augment-dropdown-menu-content";function qu(e,t,n){let s,r,a,o=ve;e.$$.on_destroy.push(()=>o());let{$$slots:i={},$$scope:l}=t,{size:u=2}=t,{onEscapeKeyDown:d=()=>{}}=t,{onClickOutside:m=()=>{}}=t,{onRequestClose:h=()=>{}}=t,{side:p="top"}=t,{align:f="center"}=t;const b={size:Yn(u)},_=b.size;Pe(e,_,y=>n(6,a=y)),jr(bt,b);const E=fe(ye.CONTEXT_KEY),C=fe(Kn.CONTEXT_KEY);return e.$$set=y=>{"size"in y&&n(10,u=y.size),"onEscapeKeyDown"in y&&n(0,d=y.onEscapeKeyDown),"onClickOutside"in y&&n(1,m=y.onClickOutside),"onRequestClose"in y&&n(2,h=y.onRequestClose),"side"in y&&n(3,p=y.side),"align"in y&&n(4,f=y.align),"$$scope"in y&&n(13,l=y.$$scope)},e.$$.update=()=>{1024&e.$$.dirty&&_.set(u)},n(5,s=C.state),o(),o=Eo(s,y=>n(14,r=y)),[d,m,h,p,f,s,a,_,E,function(y){if(r.open&&y.key==="Tab"&&!y.shiftKey){if(E.getCurrentFocusedIdx()!==void 0)return;y.preventDefault(),E==null||E.focusIdx(0)}},u,i,function(y){Te.call(this,e,y)},l]}const Hu=e=>({}),$r=e=>({}),Gu=e=>({}),Fr=e=>({});function Ur(e){let t,n;const s=e[14].iconLeft,r=ue(s,e,e[18],Fr);return{c(){t=Q("div"),r&&r.c(),N(t,"class","c-dropdown-menu-augment__item-icon svelte-toijgi")},m(a,o){M(a,t,o),r&&r.m(t,null),n=!0},p(a,o){r&&r.p&&(!n||262144&o)&&ce(r,s,a,a[18],n?me(s,a[18],o,Gu):de(a[18]),Fr)},i(a){n||(T(r,a),n=!0)},o(a){S(r,a),n=!1},d(a){a&&A(t),r&&r.d(a)}}}function Vu(e){let t;const n=e[14].default,s=ue(n,e,e[18],null);return{c(){s&&s.c()},m(r,a){s&&s.m(r,a),t=!0},p(r,a){s&&s.p&&(!t||262144&a)&&ce(s,n,r,r[18],t?me(n,r[18],a,null):de(r[18]),null)},i(r){t||(T(s,r),t=!0)},o(r){S(s,r),t=!1},d(r){s&&s.d(r)}}}function Pr(e){let t,n;const s=e[14].iconRight,r=ue(s,e,e[18],$r);return{c(){t=Q("div"),r&&r.c(),N(t,"class","c-dropdown-menu-augment__item-icon svelte-toijgi")},m(a,o){M(a,t,o),r&&r.m(t,null),n=!0},p(a,o){r&&r.p&&(!n||262144&o)&&ce(r,s,a,a[18],n?me(s,a[18],o,Hu):de(a[18]),$r)},i(a){n||(T(r,a),n=!0)},o(a){S(r,a),n=!1},d(a){a&&A(t),r&&r.d(a)}}}function Bu(e){let t,n,s,r,a,o=e[11].iconLeft&&Ur(e);n=new zr({props:{size:e[7],$$slots:{default:[Vu]},$$scope:{ctx:e}}});let i=e[11].iconRight&&Pr(e);return{c(){o&&o.c(),t=mt(),Z(n.$$.fragment),s=mt(),i&&i.c(),r=Xr()},m(l,u){o&&o.m(l,u),M(l,t,u),J(n,l,u),M(l,s,u),i&&i.m(l,u),M(l,r,u),a=!0},p(l,u){l[11].iconLeft?o?(o.p(l,u),2048&u&&T(o,1)):(o=Ur(l),o.c(),T(o,1),o.m(t.parentNode,t)):o&&(ct(),S(o,1,1,()=>{o=null}),dt());const d={};128&u&&(d.size=l[7]),262144&u&&(d.$$scope={dirty:u,ctx:l}),n.$set(d),l[11].iconRight?i?(i.p(l,u),2048&u&&T(i,1)):(i=Pr(l),i.c(),T(i,1),i.m(r.parentNode,r)):i&&(ct(),S(i,1,1,()=>{i=null}),dt())},i(l){a||(T(o),T(n.$$.fragment,l),T(i),a=!0)},o(l){S(o),S(n.$$.fragment,l),S(i),a=!1},d(l){l&&(A(t),A(s),A(r)),o&&o.d(l),X(n,l),i&&i.d(l)}}}function Yu(e){let t,n;const s=[{class:e[5]},{size:e[7]},{variant:e[4]?"solid":"ghost"},{color:e[2]??(e[4]?"accent":"neutral")},{highContrast:!e[2]&&!e[4]},{alignment:"left"},{disabled:e[1]},St("dropdown-menu-item","highlighted",e[0]),St("dropdown-menu-item","disabled",e[1]),e[6]];let r={$$slots:{default:[Bu]},$$scope:{ctx:e}};for(let a=0;a<s.length;a+=1)r=D(r,s[a]);return t=new ho({props:r}),t.$on("click",e[15]),t.$on("mouseover",e[16]),t.$on("mouseleave",e[17]),t.$on("mousedown",ju),{c(){Z(t.$$.fragment)},m(a,o){J(t,a,o),n=!0},p(a,[o]){const i=247&o?He(s,[32&o&&{class:a[5]},128&o&&{size:a[7]},16&o&&{variant:a[4]?"solid":"ghost"},20&o&&{color:a[2]??(a[4]?"accent":"neutral")},20&o&&{highContrast:!a[2]&&!a[4]},s[5],2&o&&{disabled:a[1]},1&o&&Ue(St("dropdown-menu-item","highlighted",a[0])),2&o&&Ue(St("dropdown-menu-item","disabled",a[1])),64&o&&Ue(a[6])]):{};264320&o&&(i.$$scope={dirty:o,ctx:a}),t.$set(i)},i(a){n||(T(t.$$.fragment,a),n=!0)},o(a){S(t.$$.fragment,a),n=!1},d(a){X(t,a)}}}const ju=e=>{e.preventDefault(),e.stopPropagation()};function Ku(e,t,n){let s,r,a;const o=["highlight","disabled","color","onSelect"];let i,l,u=le(t,o),{$$slots:d={},$$scope:m}=t;const h=Wr(d);let{highlight:p}=t,{disabled:f}=t,{color:b}=t,{onSelect:_=()=>{}}=t,E=!1;const C=fe(bt),y=fe(Kn.CONTEXT_KEY),oe=fe(ye.CONTEXT_KEY),Ne=C.size;Pe(e,Ne,w=>n(7,l=w));const H=y.state;function Ge(w){var Ve;if(f)return;const Qe=(Ve=oe.rootElement)==null?void 0:Ve.querySelectorAll(`.${ye.ITEM_CLASS}`);if(!Qe)return;const G=Array.from(Qe).findIndex(on=>on===w);G!==-1&&oe.setFocusedIdx(G)}return Pe(e,H,w=>n(13,i=w)),e.$$set=w=>{t=D(D({},t),Oe(w)),n(22,u=le(t,o)),"highlight"in w&&n(0,p=w.highlight),"disabled"in w&&n(1,f=w.disabled),"color"in w&&n(2,b=w.color),"onSelect"in w&&n(3,_=w.onSelect),"$$scope"in w&&n(18,m=w.$$scope)},e.$$.update=()=>{n(12,{class:s,...r}=u,s,(n(6,r),n(22,u))),4099&e.$$.dirty&&n(5,a=[f?"":ye.ITEM_CLASS,"c-dropdown-menu-augment__item",p?"c-dropdown-menu-augment__item--highlighted":"",s].join(" ")),8192&e.$$.dirty&&(i.open||n(4,E=!1))},[p,f,b,_,E,a,r,l,Ne,H,Ge,h,s,i,d,w=>{w.currentTarget instanceof HTMLElement&&Ge(w.currentTarget),_(w)},w=>{n(4,E=!0),w.currentTarget instanceof HTMLElement&&Ge(w.currentTarget)},()=>{n(4,E=!1)},m]}class gs extends ne{constructor(t){super(),se(this,t,Ku,Yu,re,{highlight:0,disabled:1,color:2,onSelect:3})}}function Wu(e){let t;const n=e[1].default,s=ue(n,e,e[2],null);return{c(){s&&s.c()},m(r,a){s&&s.m(r,a),t=!0},p(r,a){s&&s.p&&(!t||4&a)&&ce(s,n,r,r[2],t?me(n,r[2],a,null):de(r[2]),null)},i(r){t||(T(s,r),t=!0)},o(r){S(s,r),t=!1},d(r){s&&s.d(r)}}}function zu(e){let t,n;return t=new Fu({props:{slot:"iconLeft"}}),{c(){Z(t.$$.fragment)},m(s,r){J(t,s,r),n=!0},p:ve,i(s){n||(T(t.$$.fragment,s),n=!0)},o(s){S(t.$$.fragment,s),n=!1},d(s){X(t,s)}}}function Xu(e){let t,n;const s=[{class:"c-dropdown-menu-augment__breadcrumb-back-chevron"},e[0]];let r={$$slots:{iconLeft:[zu],default:[Wu]},$$scope:{ctx:e}};for(let a=0;a<s.length;a+=1)r=D(r,s[a]);return t=new gs({props:r}),{c(){Z(t.$$.fragment)},m(a,o){J(t,a,o),n=!0},p(a,[o]){const i=1&o?He(s,[s[0],Ue(a[0])]):{};4&o&&(i.$$scope={dirty:o,ctx:a}),t.$set(i)},i(a){n||(T(t.$$.fragment,a),n=!0)},o(a){S(t.$$.fragment,a),n=!1},d(a){X(t,a)}}}function Ju(e,t,n){const s=[];let r=le(t,s),{$$slots:a={},$$scope:o}=t;return e.$$set=i=>{t=D(D({},t),Oe(i)),n(0,r=le(t,s)),"$$scope"in i&&n(2,o=i.$$scope)},[r,a,o]}function Zu(e){let t,n,s=[{xmlns:"http://www.w3.org/2000/svg"},{width:"16"},{height:"16"},{"data-ds-icon":"fa"},{viewBox:"0 0 16 16"},e[0]],r={};for(let a=0;a<s.length;a+=1)r=D(r,s[a]);return{c(){t=Dn("svg"),n=new wo(!0),this.h()},l(a){t=So(a,"svg",{xmlns:!0,width:!0,height:!0,"data-ds-icon":!0,viewBox:!0});var o=Io(t);n=No(o,!0),o.forEach(A),this.h()},h(){n.a=null,bs(t,r)},m(a,o){To(a,t,o),n.m('<path fill-opacity=".01" d="M0 0h16v16H0z"/><path fill-opacity=".365" d="M10.149 7.602a.56.56 0 0 1 0 .794l-3.5 3.502a.562.562 0 0 1-.795-.795L8.956 8 5.852 4.898a.562.562 0 0 1 .795-.795z"/>',t)},p(a,[o]){bs(t,r=He(s,[{xmlns:"http://www.w3.org/2000/svg"},{width:"16"},{height:"16"},{"data-ds-icon":"fa"},{viewBox:"0 0 16 16"},1&o&&a[0]]))},i:ve,o:ve,d(a){a&&A(t)}}}function Qu(e,t,n){return e.$$set=s=>{n(0,t=D(D({},t),Oe(s)))},[t=Oe(t)]}class ec extends ne{constructor(t){super(),se(this,t,Qu,Zu,re,{})}}function tc(e){let t;const n=e[1].default,s=ue(n,e,e[2],null);return{c(){s&&s.c()},m(r,a){s&&s.m(r,a),t=!0},p(r,a){s&&s.p&&(!t||4&a)&&ce(s,n,r,r[2],t?me(n,r[2],a,null):de(r[2]),null)},i(r){t||(T(s,r),t=!0)},o(r){S(s,r),t=!1},d(r){s&&s.d(r)}}}function nc(e){let t,n;return t=new ec({props:{slot:"iconRight"}}),{c(){Z(t.$$.fragment)},m(s,r){J(t,s,r),n=!0},p:ve,i(s){n||(T(t.$$.fragment,s),n=!0)},o(s){S(t.$$.fragment,s),n=!1},d(s){X(t,s)}}}function sc(e){let t,n;const s=[{class:"c-dropdown-menu-augment__breadcrumb-chevron"},e[0]];let r={$$slots:{iconRight:[nc],default:[tc]},$$scope:{ctx:e}};for(let a=0;a<s.length;a+=1)r=D(r,s[a]);return t=new gs({props:r}),{c(){Z(t.$$.fragment)},m(a,o){J(t,a,o),n=!0},p(a,[o]){const i=1&o?He(s,[s[0],Ue(a[0])]):{};4&o&&(i.$$scope={dirty:o,ctx:a}),t.$set(i)},i(a){n||(T(t.$$.fragment,a),n=!0)},o(a){S(t.$$.fragment,a),n=!1},d(a){X(t,a)}}}function rc(e,t,n){const s=[];let r=le(t,s),{$$slots:a={},$$scope:o}=t;return e.$$set=i=>{t=D(D({},t),Oe(i)),n(0,r=le(t,s)),"$$scope"in i&&n(2,o=i.$$scope)},[r,a,o]}function ac(e){let t;const n=e[3].default,s=ue(n,e,e[4],null);return{c(){s&&s.c()},m(r,a){s&&s.m(r,a),t=!0},p(r,a){s&&s.p&&(!t||16&a)&&ce(s,n,r,r[4],t?me(n,r[4],a,null):de(r[4]),null)},i(r){t||(T(s,r),t=!0)},o(r){S(s,r),t=!1},d(r){s&&s.d(r)}}}function oc(e){let t,n,s,r;return n=new zr({props:{size:e[0],weight:"regular",$$slots:{default:[ac]},$$scope:{ctx:e}}}),{c(){t=Q("div"),Z(n.$$.fragment),N(t,"class",s=Ee(e[1])+" svelte-gehsvg")},m(a,o){M(a,t,o),J(n,t,null),r=!0},p(a,[o]){const i={};1&o&&(i.size=a[0]),16&o&&(i.$$scope={dirty:o,ctx:a}),n.$set(i),(!r||2&o&&s!==(s=Ee(a[1])+" svelte-gehsvg"))&&N(t,"class",s)},i(a){r||(T(n.$$.fragment,a),r=!0)},o(a){S(n.$$.fragment,a),r=!1},d(a){a&&A(t),X(n)}}}function ic(e,t,n){let s,r,{$$slots:a={},$$scope:o}=t;const i=fe(bt).size;return Pe(e,i,l=>n(0,r=l)),e.$$set=l=>{"$$scope"in l&&n(4,o=l.$$scope)},e.$$.update=()=>{1&e.$$.dirty&&n(1,s=["c-dropdown-menu-augment__label-item",`c-dropdown-menu-augment__label-item--size-${r}`].join(" "))},[r,s,i,a,o]}function lc(e){let t;const n=e[16].default,s=ue(n,e,e[18],null);return{c(){s&&s.c()},m(r,a){s&&s.m(r,a),t=!0},p(r,a){s&&s.p&&(!t||262144&a)&&ce(s,n,r,r[18],t?me(n,r[18],a,null):de(r[18]),null)},i(r){t||(T(s,r),t=!0)},o(r){S(s,r),t=!1},d(r){s&&s.d(r)}}}function uc(e){let t,n;const s=[{defaultOpen:e[0]},{open:e[1]},{onOpenChange:e[2]},{delayDurationMs:e[3]},{onHoverStart:e[5]},{onHoverEnd:e[6]},{triggerOn:e[7]},{nested:e[4]},e[9]];let r={$$slots:{default:[lc]},$$scope:{ctx:e}};for(let a=0;a<s.length;a+=1)r=D(r,s[a]);return t=new Do({props:r}),e[17](t),{c(){Z(t.$$.fragment)},m(a,o){J(t,a,o),n=!0},p(a,[o]){const i=767&o?He(s,[1&o&&{defaultOpen:a[0]},2&o&&{open:a[1]},4&o&&{onOpenChange:a[2]},8&o&&{delayDurationMs:a[3]},32&o&&{onHoverStart:a[5]},64&o&&{onHoverEnd:a[6]},128&o&&{triggerOn:a[7]},16&o&&{nested:a[4]},512&o&&Ue(a[9])]):{};262144&o&&(i.$$scope={dirty:o,ctx:a}),t.$set(i)},i(a){n||(T(t.$$.fragment,a),n=!0)},o(a){S(t.$$.fragment,a),n=!1},d(a){e[17](null),X(t,a)}}}function cc(e,t,n){const s=["defaultOpen","open","onOpenChange","delayDurationMs","nested","onHoverStart","onHoverEnd","triggerOn","requestOpen","requestClose","focusIdx","setFocusedIdx","getCurrentFocusedIdx","focusedIndex"];let r,a=le(t,s),{$$slots:o={},$$scope:i}=t,{defaultOpen:l}=t,{open:u}=t,{onOpenChange:d}=t,{delayDurationMs:m}=t,{nested:h}=t,{onHoverStart:p=()=>{}}=t,{onHoverEnd:f=()=>{}}=t,{triggerOn:b=[$o.Click]}=t;const _=fe(ye.CONTEXT_KEY),E=new ye(_);jr(ye.CONTEXT_KEY,E);const C=E.focusedIndex;return e.$$set=y=>{t=D(D({},t),Oe(y)),n(9,a=le(t,s)),"defaultOpen"in y&&n(0,l=y.defaultOpen),"open"in y&&n(1,u=y.open),"onOpenChange"in y&&n(2,d=y.onOpenChange),"delayDurationMs"in y&&n(3,m=y.delayDurationMs),"nested"in y&&n(4,h=y.nested),"onHoverStart"in y&&n(5,p=y.onHoverStart),"onHoverEnd"in y&&n(6,f=y.onHoverEnd),"triggerOn"in y&&n(7,b=y.triggerOn),"$$scope"in y&&n(18,i=y.$$scope)},[l,u,d,m,h,p,f,b,r,a,()=>r==null?void 0:r.requestOpen(),()=>r==null?void 0:r.requestClose(),y=>E.focusIdx(y),y=>E.setFocusedIdx(y),()=>E.getCurrentFocusedIdx(),C,o,function(y){jn[y?"unshift":"push"](()=>{r=y,n(8,r)})},i]}function dc(e){let t,n;return{c(){t=Q("div"),N(t,"class",n=Ee(`c-separator c-separator--size-${e[0]===.5?"0_5":e[0]} c-separator--orientation-${e[1]} ${e[3]}`)+" svelte-o0csoy"),xe(t,"c-separator--current-color",e[2])},m(s,r){M(s,t,r)},p(s,[r]){11&r&&n!==(n=Ee(`c-separator c-separator--size-${s[0]===.5?"0_5":s[0]} c-separator--orientation-${s[1]} ${s[3]}`)+" svelte-o0csoy")&&N(t,"class",n),15&r&&xe(t,"c-separator--current-color",s[2])},i:ve,o:ve,d(s){s&&A(t)}}}function mc(e,t,n){let{size:s=1}=t,{orientation:r="horizontal"}=t,{useCurrentColor:a=!1}=t,{class:o=""}=t;return e.$$set=i=>{"size"in i&&n(0,s=i.size),"orientation"in i&&n(1,r=i.orientation),"useCurrentColor"in i&&n(2,a=i.useCurrentColor),"class"in i&&n(3,o=i.class)},[s,r,a,o]}class hc extends ne{constructor(t){super(),se(this,t,mc,dc,re,{size:0,orientation:1,useCurrentColor:2,class:3})}}function pc(e){let t,n,s,r;return n=new hc({props:{size:4,orientation:"horizontal"}}),{c(){t=Q("div"),Z(n.$$.fragment),N(t,"class",s=Ee(`c-dropdown-menu-augment__separator c-dropdown-menu-augment__separator--size-${e[0]}`)+" svelte-24h9u")},m(a,o){M(a,t,o),J(n,t,null),r=!0},p(a,[o]){(!r||1&o&&s!==(s=Ee(`c-dropdown-menu-augment__separator c-dropdown-menu-augment__separator--size-${a[0]}`)+" svelte-24h9u"))&&N(t,"class",s)},i(a){r||(T(n.$$.fragment,a),r=!0)},o(a){S(n.$$.fragment,a),r=!1},d(a){a&&A(t),X(n)}}}function gc(e,t,n){let s;const r=fe(bt).size;return Pe(e,r,a=>n(0,s=a)),[s,r]}const fc=e=>({}),Lr=e=>({}),yc=e=>({}),qr=e=>({}),_c=e=>({}),Hr=e=>({});function Gr(e){let t,n;const s=e[11].label,r=ue(s,e,e[22],Hr);return{c(){t=Q("label"),r&&r.c(),N(t,"class","c-text-field-label svelte-vuqlvc"),N(t,"for",e[7])},m(a,o){M(a,t,o),r&&r.m(t,null),n=!0},p(a,o){r&&r.p&&(!n||4194304&o)&&ce(r,s,a,a[22],n?me(s,a[22],o,_c):de(a[22]),Hr),(!n||128&o)&&N(t,"for",a[7])},i(a){n||(T(r,a),n=!0)},o(a){S(r,a),n=!1},d(a){a&&A(t),r&&r.d(a)}}}function Vr(e){let t,n;const s=e[11].iconLeft,r=ue(s,e,e[22],qr);return{c(){t=Q("div"),r&&r.c(),N(t,"class","c-text-field__slot c-base-text-input__slot")},m(a,o){M(a,t,o),r&&r.m(t,null),n=!0},p(a,o){r&&r.p&&(!n||4194304&o)&&ce(r,s,a,a[22],n?me(s,a[22],o,yc):de(a[22]),qr)},i(a){n||(T(r,a),n=!0)},o(a){S(r,a),n=!1},d(a){a&&A(t),r&&r.d(a)}}}function Br(e){let t,n;const s=e[11].iconRight,r=ue(s,e,e[22],Lr);return{c(){t=Q("div"),r&&r.c(),N(t,"class","c-text-field__slot c-base-text-input__slot")},m(a,o){M(a,t,o),r&&r.m(t,null),n=!0},p(a,o){r&&r.p&&(!n||4194304&o)&&ce(r,s,a,a[22],n?me(s,a[22],o,fc):de(a[22]),Lr)},i(a){n||(T(r,a),n=!0)},o(a){S(r,a),n=!1},d(a){a&&A(t),r&&r.d(a)}}}function bc(e){let t,n,s,r,a,o,i,l,u=e[9].iconLeft&&Vr(e),d=[{spellcheck:"false"},{class:s=`c-text-field__input c-base-text-input__input ${e[6]}`},{id:e[7]},e[5]],m={};for(let p=0;p<d.length;p+=1)m=D(m,d[p]);let h=e[9].iconRight&&Br(e);return{c(){u&&u.c(),t=mt(),n=Q("input"),r=mt(),h&&h.c(),a=Xr(),vs(n,m),xe(n,"svelte-vuqlvc",!0)},m(p,f){u&&u.m(p,f),M(p,t,f),M(p,n,f),n.autofocus&&n.focus(),e[20](n),Es(n,e[1]),M(p,r,f),h&&h.m(p,f),M(p,a,f),o=!0,i||(l=[pe(n,"input",e[21]),pe(n,"change",e[8]),pe(n,"click",e[12]),pe(n,"keydown",e[13]),pe(n,"input",e[14]),pe(n,"blur",e[15]),pe(n,"dblclick",e[16]),pe(n,"focus",e[17]),pe(n,"mouseup",e[18]),pe(n,"selectionchange",e[19])],i=!0)},p(p,f){p[9].iconLeft?u?(u.p(p,f),512&f&&T(u,1)):(u=Vr(p),u.c(),T(u,1),u.m(t.parentNode,t)):u&&(ct(),S(u,1,1,()=>{u=null}),dt()),vs(n,m=He(d,[{spellcheck:"false"},(!o||64&f&&s!==(s=`c-text-field__input c-base-text-input__input ${p[6]}`))&&{class:s},(!o||128&f)&&{id:p[7]},32&f&&p[5]])),2&f&&n.value!==p[1]&&Es(n,p[1]),xe(n,"svelte-vuqlvc",!0),p[9].iconRight?h?(h.p(p,f),512&f&&T(h,1)):(h=Br(p),h.c(),T(h,1),h.m(a.parentNode,a)):h&&(ct(),S(h,1,1,()=>{h=null}),dt())},i(p){o||(T(u),T(h),o=!0)},o(p){S(u),S(h),o=!1},d(p){p&&(A(t),A(n),A(r),A(a)),u&&u.d(p),e[20](null),h&&h.d(p),i=!1,Co(l)}}}function vc(e){let t,n,s,r,a=e[9].label&&Gr(e);return s=new Uo({props:{variant:e[2],size:e[3],color:e[4],$$slots:{default:[bc]},$$scope:{ctx:e}}}),{c(){t=Q("div"),a&&a.c(),n=mt(),Z(s.$$.fragment),N(t,"class","c-text-field svelte-vuqlvc"),xe(t,"c-text-field--has-left-icon",e[9].iconLeft!==void 0),xe(t,"c-text-field--has-right-icon",e[9].iconRight!==void 0)},m(o,i){M(o,t,i),a&&a.m(t,null),Yr(t,n),J(s,t,null),r=!0},p(o,[i]){o[9].label?a?(a.p(o,i),512&i&&T(a,1)):(a=Gr(o),a.c(),T(a,1),a.m(t,n)):a&&(ct(),S(a,1,1,()=>{a=null}),dt());const l={};4&i&&(l.variant=o[2]),8&i&&(l.size=o[3]),16&i&&(l.color=o[4]),4195043&i&&(l.$$scope={dirty:i,ctx:o}),s.$set(l),(!r||512&i)&&xe(t,"c-text-field--has-left-icon",o[9].iconLeft!==void 0),(!r||512&i)&&xe(t,"c-text-field--has-right-icon",o[9].iconRight!==void 0)},i(o){r||(T(a),T(s.$$.fragment,o),r=!0)},o(o){S(a),S(s.$$.fragment,o),r=!1},d(o){o&&A(t),a&&a.d(),X(s)}}}function Ec(e,t,n){let s,r,a;const o=["variant","size","color","textInput","value","id"];let i=le(t,o),{$$slots:l={},$$scope:u}=t;const d=Wr(l),m=xo();let{variant:h="surface"}=t,{size:p=2}=t,{color:f}=t,{textInput:b}=t,{value:_=""}=t,{id:E}=t;const C=`text-field-${Math.random().toString(36).substring(2,11)}`;return e.$$set=y=>{t=D(D({},t),Oe(y)),n(25,i=le(t,o)),"variant"in y&&n(2,h=y.variant),"size"in y&&n(3,p=y.size),"color"in y&&n(4,f=y.color),"textInput"in y&&n(0,b=y.textInput),"value"in y&&n(1,_=y.value),"id"in y&&n(10,E=y.id),"$$scope"in y&&n(22,u=y.$$scope)},e.$$.update=()=>{1024&e.$$.dirty&&n(7,s=E||C),n(6,{class:r,...a}=i,r,(n(5,a),n(25,i)))},[b,_,h,p,f,a,r,s,function(y){m("change",y)},d,E,l,function(y){Te.call(this,e,y)},function(y){Te.call(this,e,y)},function(y){Te.call(this,e,y)},function(y){Te.call(this,e,y)},function(y){Te.call(this,e,y)},function(y){Te.call(this,e,y)},function(y){Te.call(this,e,y)},function(y){Te.call(this,e,y)},function(y){jn[y?"unshift":"push"](()=>{b=y,n(0,b)})},function(){_=this.value,n(1,_)},u]}class Tc extends ne{constructor(t){super(),se(this,t,Ec,vc,re,{variant:2,size:3,color:4,textInput:0,value:1,id:10})}}function Sc(e){let t,n,s,r,a;const o=[{class:ye.ITEM_CLASS},{size:e[1]},e[4]];function i(u){e[5](u)}let l={};for(let u=0;u<o.length;u+=1)l=D(l,o[u]);return e[0]!==void 0&&(l.value=e[0]),n=new Tc({props:l}),jn.push(()=>ko(n,"value",i)),{c(){t=Q("div"),Z(n.$$.fragment),N(t,"class",r=Ee(e[2])+" svelte-1xu00bc")},m(u,d){M(u,t,d),J(n,t,null),a=!0},p(u,[d]){const m=18&d?He(o,[o[0],2&d&&{size:u[1]},16&d&&Ue(u[4])]):{};!s&&1&d&&(s=!0,m.value=u[0],Ro(()=>s=!1)),n.$set(m),(!a||4&d&&r!==(r=Ee(u[2])+" svelte-1xu00bc"))&&N(t,"class",r)},i(u){a||(T(n.$$.fragment,u),a=!0)},o(u){S(n.$$.fragment,u),a=!1},d(u){u&&A(t),X(n)}}}function Ic(e,t,n){let s;const r=["value"];let a,o=le(t,r),{value:i=""}=t;const l=fe(bt).size;return Pe(e,l,u=>n(1,a=u)),e.$$set=u=>{t=D(D({},t),Oe(u)),n(4,o=le(t,r)),"value"in u&&n(0,i=u.value)},e.$$.update=()=>{2&e.$$.dirty&&n(2,s=["c-dropdown-menu-augment__text-field-item",`c-dropdown-menu-augment__text-field-item--size-${a}`].join(" "))},[i,a,s,l,o,function(u){i=u,n(0,i)}]}function Nc(e){let t,n,s,r;const a=e[4].default,o=ue(a,e,e[5],null);return{c(){t=Q("div"),o&&o.c()},m(i,l){M(i,t,l),o&&o.m(t,null),n=!0,s||(r=Kr(e[1].registerTrigger(t)),s=!0)},p(i,l){o&&o.p&&(!n||32&l)&&ce(o,a,i,i[5],n?me(a,i[5],l,null):de(i[5]),null)},i(i){n||(T(o,i),n=!0)},o(i){S(o,i),n=!1},d(i){i&&A(t),o&&o.d(i),s=!1,r()}}}function wc(e){let t,n;return t=new Fo({props:{referenceClientRect:e[0],$$slots:{default:[Nc]},$$scope:{ctx:e}}}),t.$on("keydown",e[3]),{c(){Z(t.$$.fragment)},m(s,r){J(t,s,r),n=!0},p(s,[r]){const a={};1&r&&(a.referenceClientRect=s[0]),32&r&&(a.$$scope={dirty:r,ctx:s}),t.$set(a)},i(s){n||(T(t.$$.fragment,s),n=!0)},o(s){S(t.$$.fragment,s),n=!1},d(s){X(t,s)}}}function xc(e,t,n){let s,{$$slots:r={},$$scope:a}=t,{referenceClientRect:o}=t;const i=fe(ye.CONTEXT_KEY),l=fe(Kn.CONTEXT_KEY).state;return Pe(e,l,u=>n(6,s=u)),e.$$set=u=>{"referenceClientRect"in u&&n(0,o=u.referenceClientRect),"$$scope"in u&&n(5,a=u.$$scope)},[o,i,l,async u=>{switch(u.key){case"ArrowUp":u.preventDefault(),u.stopPropagation(),s.open||await i.clickFocusedItem(),i==null||i.focusIdx(-1);break;case"ArrowDown":u.preventDefault(),u.stopPropagation(),s.open||await i.clickFocusedItem(),i==null||i.focusIdx(0);break;case"Enter":u.preventDefault(),u.stopPropagation(),i==null||i.clickFocusedItem()}},r,a]}const Td={BreadcrumbBackItem:class extends ne{constructor(e){super(),se(this,e,Ju,Xu,re,{})}},BreadcrumbItem:class extends ne{constructor(e){super(),se(this,e,rc,sc,re,{})}},Content:class extends ne{constructor(e){super(),se(this,e,qu,Lu,re,{size:10,onEscapeKeyDown:0,onClickOutside:1,onRequestClose:2,side:3,align:4})}},Item:gs,Label:class extends ne{constructor(e){super(),se(this,e,ic,oc,re,{})}},Root:class extends ne{constructor(e){super(),se(this,e,cc,uc,re,{defaultOpen:0,open:1,onOpenChange:2,delayDurationMs:3,nested:4,onHoverStart:5,onHoverEnd:6,triggerOn:7,requestOpen:10,requestClose:11,focusIdx:12,setFocusedIdx:13,getCurrentFocusedIdx:14,focusedIndex:15})}get requestOpen(){return this.$$.ctx[10]}get requestClose(){return this.$$.ctx[11]}get focusIdx(){return this.$$.ctx[12]}get setFocusedIdx(){return this.$$.ctx[13]}get getCurrentFocusedIdx(){return this.$$.ctx[14]}get focusedIndex(){return this.$$.ctx[15]}},Separator:class extends ne{constructor(e){super(),se(this,e,gc,pc,re,{})}},TextFieldItem:class extends ne{constructor(e){super(),se(this,e,Ic,Sc,re,{value:0})}},Trigger:class extends ne{constructor(e){super(),se(this,e,xc,wc,re,{referenceClientRect:0})}}};export{Hc as $,Uc as A,Gc as B,Ed as C,Td as D,yd as E,Bi as F,_u as G,Kc as H,vl as I,Ya as J,Tl as K,Sl as L,Yi as M,Ol as N,Il as O,wl as P,Nl as Q,oo as R,K as S,Tc as T,Wc as U,qc as V,qa as W,zc as X,Bc as Y,Yc as Z,jc as _,gr as a,sd as a$,El as a0,xl as a1,U as a2,ec as a3,ji as a4,id as a5,nl as a6,Zc as a7,$e as a8,Ga as a9,ae as aA,cd as aB,Pt as aC,pd as aD,Dr as aE,rl as aF,al as aG,We as aH,sl as aI,Fu as aJ,Jc as aK,Va as aL,Ji as aM,Qc as aN,gu as aO,to as aP,nd as aQ,td as aR,ps as aS,fu as aT,yu as aU,Yt as aV,Al as aW,Ba as aX,ul as aY,ll as aZ,rd as a_,zi as aa,Ml as ab,Wi as ac,Or as ad,el as ae,tl as af,Rl as ag,kl as ah,Xc as ai,ed as aj,ud as ak,vd as al,Pc as am,Vc as an,hc as ao,Bt as ap,od as aq,Cu as ar,ld as as,ku as at,lt as au,md as av,hd as aw,dd as ax,il as ay,ol as az,Zi as b,fd as b0,gd as b1,I as c,x as d,Qi as e,hl as f,pl as g,Xi as h,$ as i,Ke as j,xu as k,ee as l,Du as m,bd as n,_d as o,On as p,nt as q,dl as r,ad as s,ml as t,gl as u,fl as v,Lc as w,yl as x,_l as y,bl as z};
