import{S as B,i as D,s as S,a as k,n as L,d as x,b as W,g as V,u as Y,v as J,w as X,x as Z,f as tt,H as et,j as I,a3 as U,a4 as _,D as h,t as d,q as m,a5 as j,E as v,G as w,o as q,p as H,c as y,N as nt,W as G,e as F,F as R,V as T,h as b,Y as O,R as st,al as ot,A as rt,ah as at}from"./SpinnerAugment-VfHtkDdv.js";import"./design-system-init-BQpWKoxZ.js";import{h as M,W as A}from"./IconButtonAugment-BlRCK7lJ.js";import{B as ct}from"./ButtonAugment-CRJIYorH.js";import{O as lt}from"./OpenFileButton-fgZNybO2.js";import{C as it,E as $t,T as pt,a as N}from"./index-C5qylk65.js";import{M as K,R as P}from"./message-broker-DxXjuHCW.js";import{M as ut,R as mt}from"./rules-model-BLO-SAZS.js";import{R as dt}from"./RulesModeSelector-Dv1BmVk6.js";import{T as ft}from"./CardAugment-CMpdst0l.js";import{l as gt}from"./lodash-Cs_Exhqr.js";import"./chat-context-DhGlDJgc.js";import"./index-B528snJk.js";import"./index-6WVCg-U8.js";import"./remote-agents-client-zf3VV9pT.js";import"./types-CGlLNakm.js";import"./ra-diff-ops-model-DMR40nRt.js";import"./TextAreaAugment-BnS2cUNC.js";import"./BaseTextInput-C9A3t790.js";import"./async-messaging-Cm1y2LK7.js";import"./file-paths-CXmnYUii.js";import"./chevron-down-DQi0HUpw.js";function ht(o){let e,n,t=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 320 512"},o[0]],s={};for(let r=0;r<t.length;r+=1)s=k(s,t[r]);return{c(){e=tt("svg"),n=new et(!0),this.h()},l(r){e=J(r,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var a=X(e);n=Z(a,!0),a.forEach(x),this.h()},h(){n.a=null,W(e,s)},m(r,a){Y(r,e,a),n.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M15 239c-9.4 9.4-9.4 24.6 0 33.9L207 465c9.4 9.4 24.6 9.4 33.9 0s9.4-24.6 0-33.9L65.9 256 241 81c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0z"/>',e)},p(r,[a]){W(e,s=V(t,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 320 512"},1&a&&r[0]]))},i:L,o:L,d(r){r&&x(e)}}}function vt(o,e,n){return o.$$set=t=>{n(0,e=k(k({},e),I(t)))},[e=I(e)]}class wt extends B{constructor(e){super(),D(this,e,vt,ht,S,{})}}function Q(o){let e,n,t,s,r,a,$;function l(c){o[11](c)}t=new G({props:{size:1,class:"c-field-label",$$slots:{default:[xt]},$$scope:{ctx:o}}});let f={placeholder:"When should this rules file be fetched by the Agent?",size:1};return o[1]!==void 0&&(f.value=o[1]),r=new pt({props:f}),U.push(()=>_(r,"value",l)),r.$on("input",o[12]),{c(){e=R("div"),n=R("div"),w(t.$$.fragment),s=T(),w(r.$$.fragment),b(n,"class","c-rule-field c-rule-field-full-width svelte-1r8al3d"),b(e,"class","c-rule-config svelte-1r8al3d")},m(c,p){y(c,e,p),F(e,n),v(t,n,null),F(n,s),v(r,n,null),$=!0},p(c,p){const i={};262144&p&&(i.$$scope={dirty:p,ctx:c}),t.$set(i);const u={};!a&&2&p&&(a=!0,u.value=c[1],j(()=>a=!1)),r.$set(u)},i(c){$||(m(t.$$.fragment,c),m(r.$$.fragment,c),$=!0)},o(c){d(t.$$.fragment,c),d(r.$$.fragment,c),$=!1},d(c){c&&x(e),h(t),h(r)}}}function xt(o){let e;return{c(){e=O("Description")},m(n,t){y(n,e,t)},d(n){n&&x(e)}}}function yt(o){let e,n,t=o[3]===P.AGENT_REQUESTED&&Q(o);return{c(){t&&t.c(),e=nt()},m(s,r){t&&t.m(s,r),y(s,e,r),n=!0},p(s,r){s[3]===P.AGENT_REQUESTED?t?(t.p(s,r),8&r&&m(t,1)):(t=Q(s),t.c(),m(t,1),t.m(e.parentNode,e)):t&&(q(),d(t,1,1,()=>{t=null}),H())},i(s){n||(m(t),n=!0)},o(s){d(t),n=!1},d(s){s&&x(e),t&&t.d(s)}}}function Et(o){let e,n;return e=new wt({props:{slot:"iconLeft"}}),{c(){w(e.$$.fragment)},m(t,s){v(e,t,s),n=!0},p:L,i(t){n||(m(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){h(e,t)}}}function Ft(o){let e,n;return e=new ct({props:{size:1,variant:"ghost-block",color:"neutral",class:"c-back-button",$$slots:{iconLeft:[Et]},$$scope:{ctx:o}}}),e.$on("click",o[8]),{c(){w(e.$$.fragment)},m(t,s){v(e,t,s),n=!0},p(t,s){const r={};262144&s&&(r.$$scope={dirty:s,ctx:t}),e.$set(r)},i(t){n||(m(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){h(e,t)}}}function bt(o){let e;return{c(){e=O("Trigger:")},m(n,t){y(n,e,t)},d(n){n&&x(e)}}}function Rt(o){let e;return{c(){e=O("Open file")},m(n,t){y(n,e,t)},d(n){n&&x(e)}}}function zt(o){let e,n;return e=new G({props:{slot:"text",size:1,$$slots:{default:[Rt]},$$scope:{ctx:o}}}),{c(){w(e.$$.fragment)},m(t,s){v(e,t,s),n=!0},p(t,s){const r={};262144&s&&(r.$$scope={dirty:s,ctx:t}),e.$set(r)},i(t){n||(m(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){h(e,t)}}}function Lt(o){let e,n,t,s,r,a,$,l,f,c,p;return s=new ft({props:{content:"Navigate back to all Rules & Guidelines",$$slots:{default:[Ft]},$$scope:{ctx:o}}}),a=new G({props:{size:1,class:"c-field-label",$$slots:{default:[bt]},$$scope:{ctx:o}}}),l=new dt({props:{onSave:o[6],rule:o[4]}}),c=new lt({props:{size:1,path:o[2],onOpenLocalFile:o[10],$$slots:{text:[zt]},$$scope:{ctx:o}}}),{c(){e=R("div"),n=R("div"),t=R("div"),w(s.$$.fragment),r=T(),w(a.$$.fragment),$=T(),w(l.$$.fragment),f=T(),w(c.$$.fragment),b(t,"class","c-trigger-section svelte-1r8al3d"),b(n,"class","l-file-controls-left svelte-1r8al3d"),b(e,"class","l-file-controls svelte-1r8al3d"),b(e,"slot","header")},m(i,u){y(i,e,u),F(e,n),F(n,t),v(s,t,null),F(t,r),v(a,t,null),F(t,$),v(l,t,null),F(e,f),v(c,e,null),p=!0},p(i,u){const C={};262144&u&&(C.$$scope={dirty:u,ctx:i}),s.$set(C);const g={};262144&u&&(g.$$scope={dirty:u,ctx:i}),a.$set(g);const E={};16&u&&(E.rule=i[4]),l.$set(E);const z={};4&u&&(z.path=i[2]),4&u&&(z.onOpenLocalFile=i[10]),262144&u&&(z.$$scope={dirty:u,ctx:i}),c.$set(z)},i(i){p||(m(s.$$.fragment,i),m(a.$$.fragment,i),m(l.$$.fragment,i),m(c.$$.fragment,i),p=!0)},o(i){d(s.$$.fragment,i),d(a.$$.fragment,i),d(l.$$.fragment,i),d(c.$$.fragment,i),p=!1},d(i){i&&x(e),h(s),h(a),h(l),h(c)}}}function Ct(o){let e,n,t;function s(a){o[14](a)}let r={saveFunction:o[13],variant:"surface",size:2,resize:"vertical",class:"markdown-editor",$$slots:{header:[Lt],default:[yt]},$$scope:{ctx:o}};return o[0]!==void 0&&(r.value=o[0]),e=new ut({props:r}),U.push(()=>_(e,"value",s)),{c(){w(e.$$.fragment)},m(a,$){v(e,a,$),t=!0},p(a,[$]){const l={};10&$&&(l.saveFunction=a[13]),262174&$&&(l.$$scope={dirty:$,ctx:a}),!n&&1&$&&(n=!0,l.value=a[0],j(()=>n=!1)),e.$set(l)},i(a){t||(m(e.$$.fragment,a),t=!0)},o(a){d(e.$$.fragment,a),t=!1},d(a){h(e,a)}}}function Mt(o,e,n){let t,s,r,{rule:a}=e,$=a.content,l=a.description;const f=new K(M),c=new it,p=new $t(M,f,c),i=new mt(f),u=async(g,E)=>{n(9,a={...a,type:g,description:E||l}),E!==void 0&&n(1,l=E);try{await i.updateRuleContent({type:g,path:t,content:$,description:E||l})}catch(z){console.error("RulesMarkdownEditor: Error in rulesModel.updateRuleContent:",z)}},C=gt.debounce(u,500);return o.$$set=g=>{"rule"in g&&n(9,a=g.rule)},o.$$.update=()=>{512&o.$$.dirty&&n(2,t=a.path),512&o.$$.dirty&&n(3,s=a.type),15&o.$$.dirty&&n(4,r={path:t,type:s,content:$,description:l})},[$,l,t,s,r,p,u,C,()=>{M.postMessage({type:A.openSettingsPage,data:"guidelines"})},a,async()=>(p.openFile({repoRoot:"",pathName:t}),"success"),function(g){l=g,n(1,l)},()=>C(s,l),()=>u(s,l),function(g){$=g,n(0,$)}]}class Tt extends B{constructor(e){super(),D(this,e,Mt,Ct,S,{rule:9})}}function Nt(o){let e;return{c(){e=R("div"),e.textContent="Loading..."},m(n,t){y(n,e,t)},p:L,i:L,o:L,d(n){n&&x(e)}}}function kt(o){let e,n;return e=new Tt({props:{rule:o[0]}}),{c(){w(e.$$.fragment)},m(t,s){v(e,t,s),n=!0},p(t,s){const r={};1&s&&(r.rule=t[0]),e.$set(r)},i(t){n||(m(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){h(e,t)}}}function At(o){let e,n,t,s,r,a;const $=[kt,Nt],l=[];function f(c,p){return c[0]!==null?0:1}return n=f(o),t=l[n]=$[n](o),{c(){e=R("div"),t.c(),b(e,"class","c-rules-container svelte-1vbu0zh")},m(c,p){y(c,e,p),l[n].m(e,null),s=!0,r||(a=st(window,"message",o[1].onMessageFromExtension),r=!0)},p(c,[p]){let i=n;n=f(c),n===i?l[n].p(c,p):(q(),d(l[i],1,1,()=>{l[i]=null}),H(),t=l[n],t?t.p(c,p):(t=l[n]=$[n](c),t.c()),m(t,1),t.m(e,null))},i(c){s||(m(t),s=!0)},o(c){d(t),s=!1},d(c){c&&x(e),l[n].d(),r=!1,a()}}}function Bt(o,e,n){let t;const s=new K(M),r=rt(null);ot(o,r,$=>n(0,t=$));const a={handleMessageFromExtension($){const l=$.data;if(l&&l.type===A.loadFile&&l){const f=l.data.content;if(f!==void 0){const c=f.replace(/^\n+/,""),p=N.getDescriptionFrontmatterKey(c),i=N.getRuleTypeFromContent(c),u=N.extractContent(c);r.set({path:l.data.pathName,content:u,type:i,description:p})}}return!0}};return at(()=>{s.registerConsumer(a),M.postMessage({type:A.rulesLoaded})}),[t,s,r]}new class extends B{constructor(o){super(),D(this,o,Bt,At,S,{})}}({target:document.getElementById("app")});
